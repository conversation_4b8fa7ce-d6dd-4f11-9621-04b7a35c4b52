/**
 * Company lookup service for Norwegian Business Register (Brønnøysundregisteret)
 * Provides functionality to search for companies and retrieve their information
 */

export interface CompanyInfo {
  /** Company name */
  name: string;
  /** Organization number (9 digits) */
  organizationNumber: string;
  /** Visiting address information */
  visitingAddress?: {
    street: string;
    postalCode: string;
    city: string;
    municipality?: string;
  };
  /** Business address (if different from visiting address) */
  businessAddress?: {
    street: string;
    postalCode: string;
    city: string;
    municipality?: string;
  };
  /** Managing director information */
  managingDirector?: {
    firstName: string;
    lastName: string;
    fullName: string;
    birthDate?: string;
  };
  /** Company status */
  status: 'active' | 'inactive' | 'dissolved';
  /** Industry code */
  industryCode?: string;
  /** Industry description */
  industryDescription?: string;
}

export interface CompanySearchResult {
  companies: CompanyInfo[];
  totalCount: number;
  hasMore: boolean;
}

export interface CompanyLookupError {
  code: 'NETWORK_ERROR' | 'API_ERROR' | 'NOT_FOUND' | 'INVALID_INPUT' | 'RATE_LIMIT';
  message: string;
  details?: string;
}

/**
 * Search for companies by name using Brønnøysundregisteret API
 * @param query Company name to search for
 * @param limit Maximum number of results to return (default: 10)
 * @returns Promise with search results or error
 */
export async function searchCompanies(
  query: string, 
  limit: number = 10
): Promise<{ success: true; data: CompanySearchResult } | { success: false; error: CompanyLookupError }> {
  try {
    // Validate input
    if (!query || query.trim().length < 2) {
      return {
        success: false,
        error: {
          code: 'INVALID_INPUT',
          message: 'Firmanavn må være minst 2 tegn langt'
        }
      };
    }

    const trimmedQuery = query.trim();

    // Use Brønnøysundregisteret's open data API
    // API documentation: https://data.brreg.no/enhetsregisteret/api/docs/index.html
    const apiUrl = new URL('https://data.brreg.no/enhetsregisteret/api/enheter');
    apiUrl.searchParams.set('navn', trimmedQuery);
    apiUrl.searchParams.set('size', limit.toString());
    apiUrl.searchParams.set('page', '0');
    
    const response = await fetch(apiUrl.toString(), {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'JobbLogg/1.0 (Company Lookup Service)'
      }
    });

    if (!response.ok) {
      if (response.status === 429) {
        return {
          success: false,
          error: {
            code: 'RATE_LIMIT',
            message: 'For mange forespørsler. Prøv igjen om litt.',
            details: `HTTP ${response.status}`
          }
        };
      }
      
      return {
        success: false,
        error: {
          code: 'API_ERROR',
          message: 'Kunne ikke hente firmainformasjon',
          details: `HTTP ${response.status}: ${response.statusText}`
        }
      };
    }

    const data = await response.json();
    
    // Transform API response to our format
    const companies: CompanyInfo[] = await Promise.all(
      (data._embedded?.enheter || []).map(async (enhet: any) => {
        const company: CompanyInfo = {
          name: enhet.navn,
          organizationNumber: enhet.organisasjonsnummer,
          status: mapStatus(enhet.organisasjonsform?.kode, enhet.konkurs, enhet.underAvvikling),
          industryCode: enhet.naeringskode1?.kode,
          industryDescription: enhet.naeringskode1?.beskrivelse
        };

      // Add visiting address if available
      if (enhet.beliggenhetsadresse) {
        const addr = enhet.beliggenhetsadresse;
        company.visitingAddress = {
          street: [addr.adresse?.[0], addr.adresse?.[1]].filter(Boolean).join(' ').trim(),
          postalCode: addr.postnummer || '',
          city: addr.poststed || '',
          municipality: addr.kommune
        };
      }

      // Add business address if available and different
      if (enhet.forretningsadresse && enhet.forretningsadresse !== enhet.beliggenhetsadresse) {
        const addr = enhet.forretningsadresse;
        company.businessAddress = {
          street: [addr.adresse?.[0], addr.adresse?.[1]].filter(Boolean).join(' ').trim(),
          postalCode: addr.postnummer || '',
          city: addr.poststed || '',
          municipality: addr.kommune
        };
      }

      // Fetch managing director information (optional)
      try {
        company.managingDirector = await fetchManagingDirector(enhet.organisasjonsnummer);
      } catch (error) {
        // Managing director info is optional for search results
        console.warn('[CompanyLookup] Managing director fetch failed for', enhet.organisasjonsnummer, error);
      }

      return company;
    }));

    return {
      success: true,
      data: {
        companies,
        totalCount: data.page?.totalElements || companies.length,
        hasMore: (data.page?.totalElements || 0) > (data.page?.size || 0)
      }
    };

  } catch (error) {
    console.error('[CompanyLookup] Search failed:', error);
    
    return {
      success: false,
      error: {
        code: 'NETWORK_ERROR',
        message: 'Nettverksfeil. Sjekk internettforbindelsen din.',
        details: error instanceof Error ? error.message : 'Unknown error'
      }
    };
  }
}

/**
 * Fetch managing director information from roles API
 * @param orgNumber Organization number (9 digits)
 * @returns Managing director info or null if not found
 */
async function fetchManagingDirector(orgNumber: string): Promise<CompanyInfo['managingDirector'] | null> {
  try {
    const apiUrl = `https://data.brreg.no/enhetsregisteret/api/enheter/${orgNumber}/roller`;

    const response = await fetch(apiUrl, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'JobbLogg/1.0 (Managing Director Lookup)'
      }
    });

    if (!response.ok) {
      // Roles API might not be available for all companies
      return null;
    }

    const rolesData = await response.json();

    // Look for "Daglig leder" (Managing Director) role
    const managingDirectorGroup = rolesData.rollegrupper?.find((group: any) =>
      group.type?.kode === 'DAGL'
    );

    if (managingDirectorGroup?.roller?.[0]?.person) {
      const person = managingDirectorGroup.roller[0].person;
      const navn = person.navn;

      return {
        firstName: navn.fornavn || '',
        lastName: navn.etternavn || '',
        fullName: [navn.fornavn, navn.mellomnavn, navn.etternavn].filter(Boolean).join(' '),
        birthDate: person.fodselsdato
      };
    }

    return null;
  } catch (error) {
    // Silently fail - managing director info is optional
    console.warn('[CompanyLookup] Failed to fetch managing director:', error);
    return null;
  }
}

/**
 * Get company information by organization number
 * @param orgNumber Organization number (9 digits)
 * @returns Promise with company information or error
 */
export async function getCompanyByOrgNumber(
  orgNumber: string
): Promise<{ success: true; data: CompanyInfo } | { success: false; error: CompanyLookupError }> {
  try {
    // Validate and clean organization number
    const cleanOrgNumber = orgNumber.replace(/\D/g, '');
    if (cleanOrgNumber.length !== 9) {
      return {
        success: false,
        error: {
          code: 'INVALID_INPUT',
          message: 'Organisasjonsnummer må være 9 siffer'
        }
      };
    }

    const apiUrl = `https://data.brreg.no/enhetsregisteret/api/enheter/${cleanOrgNumber}`;
    
    const response = await fetch(apiUrl, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'JobbLogg/1.0 (Company Lookup Service)'
      }
    });

    if (response.status === 404) {
      return {
        success: false,
        error: {
          code: 'NOT_FOUND',
          message: 'Fant ikke firma med dette organisasjonsnummeret'
        }
      };
    }

    if (!response.ok) {
      return {
        success: false,
        error: {
          code: 'API_ERROR',
          message: 'Kunne ikke hente firmainformasjon',
          details: `HTTP ${response.status}: ${response.statusText}`
        }
      };
    }

    const enhet = await response.json();

    // Fetch managing director information in parallel
    const managingDirectorPromise = fetchManagingDirector(cleanOrgNumber);

    // Transform to our format (same logic as search)
    const company: CompanyInfo = {
      name: enhet.navn,
      organizationNumber: enhet.organisasjonsnummer,
      status: mapStatus(enhet.organisasjonsform?.kode, enhet.konkurs, enhet.underAvvikling),
      industryCode: enhet.naeringskode1?.kode,
      industryDescription: enhet.naeringskode1?.beskrivelse
    };

    // Add addresses
    if (enhet.beliggenhetsadresse) {
      const addr = enhet.beliggenhetsadresse;
      company.visitingAddress = {
        street: [addr.adresse?.[0], addr.adresse?.[1]].filter(Boolean).join(' ').trim(),
        postalCode: addr.postnummer || '',
        city: addr.poststed || '',
        municipality: addr.kommune
      };
    }

    if (enhet.forretningsadresse && enhet.forretningsadresse !== enhet.beliggenhetsadresse) {
      const addr = enhet.forretningsadresse;
      company.businessAddress = {
        street: [addr.adresse?.[0], addr.adresse?.[1]].filter(Boolean).join(' ').trim(),
        postalCode: addr.postnummer || '',
        city: addr.poststed || '',
        municipality: addr.kommune
      };
    }

    // Wait for managing director information
    try {
      company.managingDirector = await managingDirectorPromise;
    } catch (error) {
      // Managing director info is optional, continue without it
      console.warn('[CompanyLookup] Managing director fetch failed:', error);
    }

    return {
      success: true,
      data: company
    };

  } catch (error) {
    console.error('[CompanyLookup] Lookup by org number failed:', error);
    
    return {
      success: false,
      error: {
        code: 'NETWORK_ERROR',
        message: 'Nettverksfeil. Sjekk internettforbindelsen din.',
        details: error instanceof Error ? error.message : 'Unknown error'
      }
    };
  }
}

/**
 * Map API status to our simplified status
 */
function mapStatus(orgForm: string, konkurs: boolean, underAvvikling: boolean): CompanyInfo['status'] {
  if (konkurs || underAvvikling) {
    return 'dissolved';
  }
  
  // Most common active organization forms
  const activeOrgForms = ['AS', 'ASA', 'ENK', 'ANS', 'DA', 'NUF', 'BA', 'BRL', 'GFS', 'SPA', 'SF', 'IKS'];
  
  if (activeOrgForms.includes(orgForm)) {
    return 'active';
  }
  
  return 'inactive';
}

/**
 * Format organization number with spaces for display
 * @param orgNumber Raw organization number
 * @returns Formatted organization number (e.g., "***********")
 */
export function formatOrgNumber(orgNumber: string): string {
  const clean = orgNumber.replace(/\D/g, '');
  if (clean.length === 9) {
    return `${clean.slice(0, 3)} ${clean.slice(3, 6)} ${clean.slice(6)}`;
  }
  return orgNumber;
}

/**
 * Validate Norwegian organization number using MOD11 algorithm
 * @param orgNumber Organization number to validate
 * @returns True if valid, false otherwise
 */
export function validateOrgNumber(orgNumber: string): boolean {
  const clean = orgNumber.replace(/\D/g, '');
  
  if (clean.length !== 9) {
    return false;
  }

  // MOD11 validation for Norwegian organization numbers
  const weights = [3, 2, 7, 6, 5, 4, 3, 2];
  let sum = 0;
  
  for (let i = 0; i < 8; i++) {
    sum += parseInt(clean[i]) * weights[i];
  }
  
  const remainder = sum % 11;
  const checkDigit = remainder === 0 ? 0 : 11 - remainder;
  
  return checkDigit === parseInt(clean[8]);
}
