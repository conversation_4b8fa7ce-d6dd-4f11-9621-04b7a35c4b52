import React from 'react';
import { useUser } from '@clerk/clerk-react';

export const UserDebug: React.FC = () => {
  const { user } = useUser();

  React.useEffect(() => {
    if (user) {
      console.log('🔍 [UserDebug] Full user object:', user);
      console.log('🔍 [UserDebug] User properties:', {
        id: user.id,
        firstName: user.firstName,
        lastName: user.lastName,
        fullName: user.fullName,
        primaryEmailAddress: user.primaryEmailAddress?.emailAddress,
        username: user.username,
        imageUrl: user.imageUrl,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
        // Check all available properties
        allKeys: Object.keys(user),
        // Check if firstName is actually undefined
        firstNameType: typeof user.firstName,
        firstNameValue: user.firstName,
        // Try alternative name properties
        name: (user as any).name,
        displayName: (user as any).displayName,
      });
    }
  }, [user]);

  if (!user) {
    return <div>No user loaded</div>;
  }

  return (
    <div style={{ 
      position: 'fixed', 
      top: '10px', 
      right: '10px', 
      background: 'white', 
      border: '1px solid #ccc', 
      padding: '10px',
      fontSize: '12px',
      maxWidth: '300px',
      zIndex: 9999
    }}>
      <h4>User Debug Info</h4>
      <p><strong>ID:</strong> {user.id}</p>
      <p><strong>firstName:</strong> {user.firstName || 'undefined'}</p>
      <p><strong>lastName:</strong> {user.lastName || 'undefined'}</p>
      <p><strong>fullName:</strong> {user.fullName || 'undefined'}</p>
      <p><strong>username:</strong> {user.username || 'undefined'}</p>
      <p><strong>email:</strong> {user.primaryEmailAddress?.emailAddress || 'undefined'}</p>
      <p><strong>Type of firstName:</strong> {typeof user.firstName}</p>
    </div>
  );
};
