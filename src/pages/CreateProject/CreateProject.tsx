import React, { useState, useEffect, useCallback, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { useMutation, useQuery } from 'convex/react';
import { useUser } from '@clerk/clerk-react';
import { api } from '../../../convex/_generated/api';
import { PageLayout, Heading2, Heading3, BodyText, TextMuted, TextInput, TextArea, FormError, SubmitButton, SelectInput, CompanyLookup, CompanyLookupRef, LockedInput, ToggleSwitch } from '../../components/ui';
import { CompanyInfo } from '../../services/companyLookup';

const STORAGE_KEY = 'jobblogg-create-project-form';

const CreateProject: React.FC = () => {
  const [showSuccess, setShowSuccess] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<{[key: string]: string}>({});
  const companyLookupRef = useRef<CompanyLookupRef>(null);
  const previousCustomerTypeRef = useRef<'privat' | 'bedrift'>('privat');
  const [formData, setFormData] = useState({
    projectName: '',
    description: '',
    // Customer data
    customerName: '',
    customerType: 'privat' as 'privat' | 'bedrift',
    contactPerson: '',
    phone: '',
    email: '',
    address: '',
    orgNumber: '',
    notes: ''
  });
  const [useExistingCustomer, setUseExistingCustomer] = useState(false);
  const [selectedCustomerId, setSelectedCustomerId] = useState<string>('');

  // Brønnøysundregisteret data tracking
  const [brregData, setBrregData] = useState<any>(null);
  const [brregFetchedAt, setBrregFetchedAt] = useState<number | null>(null);
  const [useCustomAddress, setUseCustomAddress] = useState(false);

  // Field locking state
  const [lockedFields, setLockedFields] = useState({
    orgNumber: false,
    address: false
  });

  // Track if company has been selected from Brønnøysundregisteret
  const [companySelected, setCompanySelected] = useState(false);

  // Store managing director info for reference (not editable)
  const [managingDirectorInfo, setManagingDirectorInfo] = useState<string>('');

  const navigate = useNavigate();
  const createProject = useMutation(api.projects.create);
  const createCustomer = useMutation(api.customers.create);
  const updateCustomer = useMutation(api.customers.update);
  const { user } = useUser();

  // Fetch existing customers for selection
  const existingCustomers = useQuery(
    api.customers.getByUser,
    user?.id ? { userId: user.id } : "skip"
  );

  // Filter existing customers based on selected customer type
  const filteredExistingCustomers = existingCustomers?.filter(customer => {
    // Handle both old 'firma' and new 'bedrift' values for backward compatibility
    const customerType = customer.type === 'firma' ? 'bedrift' : customer.type;
    return customerType === formData.customerType;
  }) || [];

  // Autosave functionality with debouncing
  const saveToLocalStorage = useCallback(() => {
    const dataToSave = {
      formData,
      useExistingCustomer,
      selectedCustomerId,
      timestamp: Date.now()
    };
    localStorage.setItem(STORAGE_KEY, JSON.stringify(dataToSave));
  }, [formData, useExistingCustomer, selectedCustomerId]);

  // Debounced save function
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      saveToLocalStorage();
    }, 500); // 500ms debounce delay

    return () => clearTimeout(timeoutId);
  }, [saveToLocalStorage]);

  // Load form data from localStorage on mount
  useEffect(() => {
    const savedData = localStorage.getItem(STORAGE_KEY);
    if (savedData) {
      try {
        const parsed = JSON.parse(savedData);
        // Only restore if data is less than 24 hours old
        if (parsed.timestamp && Date.now() - parsed.timestamp < 24 * 60 * 60 * 1000) {
          setFormData(parsed.formData || formData);
          setUseExistingCustomer(parsed.useExistingCustomer || false);
          setSelectedCustomerId(parsed.selectedCustomerId || '');
        }
      } catch (error) {
        console.error('Error loading saved form data:', error);
      }
    }
  }, []);

  // Reset form when customer type changes to prevent data contamination
  useEffect(() => {
    // Only reset if customer type actually changed (not on initial load or form updates)
    if (previousCustomerTypeRef.current !== formData.customerType) {
      console.log('🔄 Customer type changed from', previousCustomerTypeRef.current, 'to', formData.customerType, '- resetting form fields');

      // Reset all customer-related form fields except customerType and project info
      setFormData(prev => ({
        ...prev,
        customerName: '',
        contactPerson: '',
        phone: '',
        email: '',
        address: '',
        orgNumber: '',
        notes: ''
      }));

      // Clear validation errors
      setErrors({});

      // Reset company lookup component
      if (companyLookupRef.current) {
        companyLookupRef.current.reset();
      }

      // Reset existing customer selection
      setUseExistingCustomer(false);
      setSelectedCustomerId('');

      // Reset company selection state
      setCompanySelected(false);
      setBrregData(null);
      setBrregFetchedAt(null);
      setUseCustomAddress(false);
      setLockedFields({
        orgNumber: false,
        address: false
      });
      setManagingDirectorInfo('');

      // Update the ref to track the new customer type
      previousCustomerTypeRef.current = formData.customerType;
    }
  }, [formData.customerType]);

  // Clear localStorage on successful submission
  const clearSavedData = () => {
    localStorage.removeItem(STORAGE_KEY);
  };

  // Dynamic label and placeholder based on customer type
  const getCustomerNameLabel = () => {
    return formData.customerType === 'bedrift' ? 'Bedriftsnavn' : 'Kundenavn';
  };

  const getCustomerNamePlaceholder = () => {
    return formData.customerType === 'bedrift' ? 'F.eks. Nordmann Bygg AS' : 'F.eks. Ola Nordmann';
  };

  // Handle updating customer information from Brønnøysundregisteret
  const handleUpdateFromBrreg = async (customer: any) => {
    if (!customer.orgNumber) {
      console.warn('No organization number found for customer');
      return;
    }

    try {
      setIsLoading(true);

      // Fetch fresh data from Brønnøysundregisteret
      const response = await fetch(`https://data.brreg.no/enhetsregisteret/api/enheter/${customer.orgNumber}`, {
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'JobbLogg/1.0 (Customer Update)'
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch company data: ${response.status}`);
      }

      const enhet = await response.json();

      // Map the response to our company format
      const updatedBrregData = {
        name: enhet.navn,
        orgNumber: enhet.organisasjonsnummer,
        managingDirector: enhet.dagligLeder?.navn || '',
        visitingAddress: null as any,
        businessAddress: null as any
      };

      // Map visiting address
      if (enhet.beliggenhetsadresse) {
        const addr = enhet.beliggenhetsadresse;
        updatedBrregData.visitingAddress = {
          street: [addr.adresse?.[0], addr.adresse?.[1]].filter(Boolean).join(' ').trim(),
          postalCode: addr.postnummer || '',
          city: addr.poststed || '',
          municipality: addr.kommune
        };
      }

      // Map business address
      if (enhet.forretningsadresse) {
        const addr = enhet.forretningsadresse;
        updatedBrregData.businessAddress = {
          street: [addr.adresse?.[0], addr.adresse?.[1]].filter(Boolean).join(' ').trim(),
          postalCode: addr.postnummer || '',
          city: addr.poststed || '',
          municipality: addr.kommune
        };
      }

      // Update the customer record with fresh data
      await updateCustomer({
        customerId: customer._id,
        userId: user?.id || '',
        brregData: updatedBrregData,
        brregFetchedAt: Date.now()
      });

      console.log('✅ Customer information updated from Brønnøysundregisteret');

    } catch (error) {
      console.error('❌ Failed to update customer from Brønnøysundregisteret:', error);
      setErrors({ brregUpdate: 'Kunne ikke oppdatere kundeinformasjon fra Brønnøysundregisteret' });
    } finally {
      setIsLoading(false);
    }
  };

  const validateForm = () => {
    const newErrors: {[key: string]: string} = {};

    // Project validation
    if (!formData.projectName || formData.projectName.trim().length < 2) {
      newErrors.projectName = 'Prosjektnavn må være minst 2 tegn langt';
    }

    if (formData.projectName && formData.projectName.length > 100) {
      newErrors.projectName = 'Prosjektnavn kan ikke være lengre enn 100 tegn';
    }

    // Customer validation (only if not using existing customer)
    if (!useExistingCustomer) {
      if (!formData.customerName || formData.customerName.trim().length < 2) {
        newErrors.customerName = 'Kundenavn må være minst 2 tegn langt';
      }

      if (!formData.address || formData.address.trim().length < 5) {
        newErrors.address = 'Adresse må være minst 5 tegn lang';
      }

      if (formData.customerType === 'bedrift' && formData.orgNumber && formData.orgNumber.trim() !== '') {
        // Basic org number validation (9 digits)
        const orgNumberPattern = /^\d{9}$/;
        if (!orgNumberPattern.test(formData.orgNumber.trim())) {
          newErrors.orgNumber = 'Organisasjonsnummer må være 9 siffer';
        }
      }

      // Enhanced validation: Make phone and email required for both customer types
      if (formData.customerType === 'bedrift') {
        // Required fields for business customers
        if (!formData.phone || formData.phone.trim() === '') {
          newErrors.phone = 'Telefonnummer til kontaktperson er påkrevd';
        }
        if (!formData.email || formData.email.trim() === '') {
          newErrors.email = 'E-postadresse til kontaktperson er påkrevd';
        }
      } else {
        // Required fields for private customers
        if (!formData.phone || formData.phone.trim() === '') {
          newErrors.phone = 'Telefonnummer er påkrevd';
        }
        if (!formData.email || formData.email.trim() === '') {
          newErrors.email = 'E-postadresse er påkrevd';
        }
      }

      // Email format validation (if email is provided)
      if (formData.email && formData.email.trim() !== '') {
        const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailPattern.test(formData.email.trim())) {
          newErrors.email = 'Ugyldig e-postadresse';
        }
      }
    } else {
      // Validate existing customer selection
      if (!selectedCustomerId) {
        newErrors.selectedCustomer = 'Velg en eksisterende kunde';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsLoading(true);
    setErrors({});

    try {
      // Validate form
      if (!validateForm()) {
        setIsLoading(false);
        return;
      }

      if (!user?.id) {
        console.error('User not authenticated');
        setIsLoading(false);
        return;
      }

      let customerId: string | undefined;

      if (useExistingCustomer) {
        // Use selected existing customer
        customerId = selectedCustomerId;
      } else {
        // Create new customer first
        const newCustomerId = await createCustomer({
          name: formData.customerName.trim(),
          type: formData.customerType,
          contactPerson: formData.contactPerson.trim() || undefined,
          phone: formData.phone.trim() || undefined,
          email: formData.email.trim() || undefined,
          address: formData.address.trim(),
          orgNumber: formData.orgNumber.trim() || undefined,
          notes: formData.notes.trim() || undefined,
          // Brønnøysundregisteret data tracking
          brregFetchedAt: brregFetchedAt || undefined,
          brregData: brregData || undefined,
          useCustomAddress: useCustomAddress || undefined,
          userId: user.id
        });
        customerId = newCustomerId;
      }

      // Create project with customer reference
      await createProject({
        name: formData.projectName.trim(),
        description: formData.description.trim(),
        userId: user.id,
        customerId: customerId as any // Type assertion for Convex ID
      });

      // Clear saved data from localStorage
      clearSavedData();

      // Reset form
      setFormData({
        projectName: '',
        description: '',
        customerName: '',
        customerType: 'privat',
        contactPerson: '',
        phone: '',
        email: '',
        address: '',
        orgNumber: '',
        notes: ''
      });
      setSelectedCustomerId('');
      setUseExistingCustomer(false);

      // Show success message
      setShowSuccess(true);
      setTimeout(() => {
        setShowSuccess(false);
        navigate('/'); // Navigate back to Dashboard after success
      }, 2000);

    } catch (error) {
      console.error('Error creating project:', error);
      setErrors({ general: 'Det oppstod en feil ved opprettelse av prosjektet. Prøv igjen.' });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <PageLayout
      title="Opprett nytt prosjekt"
      showBackButton
      backUrl="/"
      headerActions={
        <BodyText className="text-lg">
          Fyll ut informasjonen nedenfor for å starte et nytt prosjekt ✨
        </BodyText>
      }
    >

        {/* Modern Success Alert */}
        {showSuccess && (
          <div className="bg-jobblogg-accent-soft border border-jobblogg-accent rounded-xl p-6 mb-8 animate-scale-in">
            <div className="flex items-center gap-4">
              <div className="p-2 bg-jobblogg-accent-soft rounded-full">
                <svg
                  className="w-6 h-6 text-jobblogg-accent"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
              </div>
              <div>
                <BodyText className="text-jobblogg-accent font-semibold">Prosjekt opprettet! 🎉</BodyText>
                <TextMuted className="text-jobblogg-accent/80 text-sm">Du blir snart omdirigert til oversikten...</TextMuted>
              </div>
            </div>
          </div>
        )}

        {/* General Error Alert */}
        {errors.general && (
          <div className="mb-8">
            <FormError message={errors.general} />
          </div>
        )}

        {/* Modern Create Project Form */}
        <div className="max-w-2xl mx-auto">
          <div className="bg-white rounded-xl shadow-lg p-8 animate-slide-up border border-jobblogg-border">
            <div className="mb-8 text-center">
              <div className="w-16 h-16 mx-auto mb-4 bg-jobblogg-primary-soft rounded-full flex items-center justify-center">
                <svg className="w-8 h-8 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
              </div>
              <Heading2 className="mb-2">Prosjektdetaljer</Heading2>
              <BodyText>Gi prosjektet ditt et navn og en beskrivelse</BodyText>
            </div>

            <form onSubmit={handleSubmit} className="space-y-8">
              {/* Project Name Field */}
              <TextInput
                label="Prosjektnavn"
                placeholder="F.eks. Kjøkkenrenovering, Terrasse bygging..."
                required
                fullWidth
                size="large"
                value={formData.projectName}
                onChange={(e) => setFormData(prev => ({ ...prev, projectName: e.target.value }))}
                error={errors.projectName}
              />

              {/* Description Field */}
              <TextArea
                label="Beskrivelse"
                placeholder="Beskriv hva prosjektet handler om, mål, eller andre viktige detaljer..."
                fullWidth
                rows={4}
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                helperText="💡 Tips: En god beskrivelse hjelper deg å holde oversikt senere"
              />

              {/* Customer Section */}
              <div className="border-t border-jobblogg-border pt-8">
                <Heading3 className="mb-6 flex items-center gap-2">
                  <svg className="w-5 h-5 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                  </svg>
                  Kundeinformasjon
                </Heading3>

                {/* Customer Selection Toggle */}
                <div className="mb-6">
                  <div className="flex items-center gap-4">
                    <label className="flex items-center gap-2 cursor-pointer">
                      <input
                        type="radio"
                        name="customerOption"
                        checked={!useExistingCustomer}
                        onChange={() => setUseExistingCustomer(false)}
                        className="w-4 h-4 text-jobblogg-primary focus:ring-jobblogg-primary border-jobblogg-border"
                      />
                      <span className="text-sm font-medium text-jobblogg-text-strong">Ny kunde</span>
                    </label>
                    <label className="flex items-center gap-2 cursor-pointer">
                      <input
                        type="radio"
                        name="customerOption"
                        checked={useExistingCustomer}
                        onChange={() => setUseExistingCustomer(true)}
                        className="w-4 h-4 text-jobblogg-primary focus:ring-jobblogg-primary border-jobblogg-border"
                      />
                      <span className="text-sm font-medium text-jobblogg-text-strong">Eksisterende kunde</span>
                    </label>
                  </div>
                </div>

                {/* Existing Customer Selection */}
                {useExistingCustomer && (
                  <div className="space-y-4">
                    {filteredExistingCustomers.length > 0 ? (
                      <>
                        <SelectInput
                          label="Velg kunde"
                          placeholder={`Velg en eksisterende ${formData.customerType === 'bedrift' ? 'bedrift' : 'privatkunde'}`}
                          required
                          fullWidth
                          value={selectedCustomerId}
                          onChange={(e) => setSelectedCustomerId(e.target.value)}
                          options={filteredExistingCustomers.map(customer => {
                            // Use structured address fields with fallback to legacy address
                            let displayAddress = 'Ingen adresse';
                            if (customer.streetAddress && customer.postalCode && customer.city) {
                              displayAddress = `${customer.streetAddress}, ${customer.postalCode} ${customer.city}`;
                            } else if (customer.address) {
                              displayAddress = customer.address;
                            }

                            return {
                              value: customer._id,
                              label: `${customer.name} - ${displayAddress}`
                            };
                          })}
                          error={errors.selectedCustomer}
                          helperText={`Velg fra dine eksisterende ${formData.customerType === 'bedrift' ? 'bedrifter' : 'privatkunder'}`}
                        />

                        {/* Selected Customer Information Display */}
                        {selectedCustomerId && (() => {
                          const selectedCustomer = filteredExistingCustomers.find(c => c._id === selectedCustomerId);
                          if (!selectedCustomer) return null;

                          return (
                            <div className="bg-jobblogg-neutral rounded-lg p-4 border border-jobblogg-border">
                              <div className="flex items-center justify-between mb-3">
                                <h4 className="font-semibold text-jobblogg-text-strong">Kundeinformasjon</h4>
                                {selectedCustomer.type === 'bedrift' && selectedCustomer.brregData && (
                                  <button
                                    type="button"
                                    onClick={() => handleUpdateFromBrreg(selectedCustomer)}
                                    disabled={isLoading}
                                    className="px-3 py-1 text-xs bg-jobblogg-primary text-white rounded-md hover:bg-jobblogg-primary-dark transition-colors disabled:opacity-50"
                                  >
                                    Oppdater fra Brønnøysundregisteret
                                  </button>
                                )}
                              </div>

                              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                <div>
                                  <span className="font-medium text-jobblogg-text-strong">Navn:</span>
                                  <span className="ml-2 text-jobblogg-text-medium">{selectedCustomer.name}</span>
                                </div>

                                {selectedCustomer.type === 'bedrift' && selectedCustomer.orgNumber && (
                                  <div>
                                    <span className="font-medium text-jobblogg-text-strong">Org.nr:</span>
                                    <span className="ml-2 text-jobblogg-text-medium">{selectedCustomer.orgNumber}</span>
                                  </div>
                                )}

                                {selectedCustomer.phone && (
                                  <div>
                                    <span className="font-medium text-jobblogg-text-strong">Telefon:</span>
                                    <span className="ml-2 text-jobblogg-text-medium">{selectedCustomer.phone}</span>
                                  </div>
                                )}

                                {selectedCustomer.email && (
                                  <div>
                                    <span className="font-medium text-jobblogg-text-strong">E-post:</span>
                                    <span className="ml-2 text-jobblogg-text-medium">{selectedCustomer.email}</span>
                                  </div>
                                )}

                                {/* Address Information */}
                                {(selectedCustomer.streetAddress || selectedCustomer.address) && (
                                  <div className="md:col-span-2">
                                    <span className="font-medium text-jobblogg-text-strong">Adresse:</span>
                                    <span className="ml-2 text-jobblogg-text-medium">
                                      {selectedCustomer.streetAddress
                                        ? `${selectedCustomer.streetAddress}${selectedCustomer.entrance ? `, ${selectedCustomer.entrance}` : ''}, ${selectedCustomer.postalCode} ${selectedCustomer.city}`
                                        : selectedCustomer.address
                                      }
                                    </span>
                                  </div>
                                )}

                                {/* Managing Director for Business Customers */}
                                {selectedCustomer.type === 'bedrift' && selectedCustomer.brregData?.managingDirector && (
                                  <div className="md:col-span-2">
                                    <span className="font-medium text-jobblogg-text-strong">Daglig leder:</span>
                                    <span className="ml-2 text-jobblogg-text-medium">{selectedCustomer.brregData.managingDirector}</span>
                                  </div>
                                )}
                              </div>
                            </div>
                          );
                        })()}
                      </>
                    ) : (
                      <div className="p-4 bg-jobblogg-card-bg border border-jobblogg-border rounded-lg">
                        <div className="text-center">
                          <TextMuted className="text-sm">
                            Ingen {formData.customerType === 'bedrift' ? 'bedrifter' : 'privatkunder'} funnet.
                          </TextMuted>
                          <TextMuted className="text-xs mt-1">
                            Opprett en ny kunde nedenfor.
                          </TextMuted>
                        </div>
                      </div>
                    )}
                  </div>
                )}

                {/* New Customer Form */}
                {!useExistingCustomer && (
                  <div className="space-y-8">
                    {/* Section 1: Basic Information */}
                    <div className="space-y-4">
                      <div className="pb-2 border-b border-jobblogg-border">
                        <Heading3 className="text-lg font-semibold text-jobblogg-text-strong">
                          Grunnleggende informasjon
                        </Heading3>
                        <TextMuted className="text-sm">Obligatorisk informasjon om kunde og prosjekt</TextMuted>
                      </div>

                      {/* Customer Type Radio Buttons */}
                      <div className="space-y-3">
                        <label className="block text-sm font-medium text-jobblogg-text-strong">
                          Kundetype <span className="text-jobblogg-error">*</span>
                        </label>
                        <div className="flex flex-col sm:flex-row gap-4">
                          <label className="flex items-center gap-3 cursor-pointer min-h-[44px] p-3 rounded-lg border border-jobblogg-border hover:border-jobblogg-primary transition-colors">
                            <input
                              type="radio"
                              name="customerType"
                              value="privat"
                              checked={formData.customerType === 'privat'}
                              onChange={(e) => setFormData(prev => ({ ...prev, customerType: e.target.value as 'privat' | 'bedrift' }))}
                              className="w-5 h-5 text-jobblogg-primary focus:ring-jobblogg-primary border-jobblogg-border"
                            />
                            <span className="text-sm font-medium text-jobblogg-text-strong">Privatkunde</span>
                          </label>
                          <label className="flex items-center gap-3 cursor-pointer min-h-[44px] p-3 rounded-lg border border-jobblogg-border hover:border-jobblogg-primary transition-colors">
                            <input
                              type="radio"
                              name="customerType"
                              value="bedrift"
                              checked={formData.customerType === 'bedrift'}
                              onChange={(e) => setFormData(prev => ({ ...prev, customerType: e.target.value as 'privat' | 'bedrift' }))}
                              className="w-5 h-5 text-jobblogg-primary focus:ring-jobblogg-primary border-jobblogg-border"
                            />
                            <span className="text-sm font-medium text-jobblogg-text-strong">Bedriftskunde</span>
                          </label>
                        </div>
                      </div>

                      {/* Dynamic Customer Name Field with Company Lookup for Bedrift */}
                      {formData.customerType === 'bedrift' ? (
                        <CompanyLookup
                          ref={companyLookupRef}
                          companyName={formData.customerName}
                          onCompanyNameChange={(name) => setFormData(prev => ({ ...prev, customerName: name }))}
                          onCompanySelect={(company: CompanyInfo) => {
                            // Store Brønnøysundregisteret data
                            const brregTimestamp = Date.now();
                            setBrregData(company);
                            setBrregFetchedAt(brregTimestamp);

                            // Auto-fill form with company information
                            setFormData(prev => ({
                              ...prev,
                              customerName: company.name,
                              orgNumber: company.organizationNumber,
                              // Use visiting address if available, otherwise business address
                              address: company.visitingAddress?.street && company.visitingAddress?.postalCode && company.visitingAddress?.city
                                ? `${company.visitingAddress.street}, ${company.visitingAddress.postalCode} ${company.visitingAddress.city}`
                                : company.businessAddress?.street && company.businessAddress?.postalCode && company.businessAddress?.city
                                ? `${company.businessAddress.street}, ${company.businessAddress.postalCode} ${company.businessAddress.city}`
                                : prev.address
                            }));

                            // Store managing director as reference information (not editable)
                            setManagingDirectorInfo(company.managingDirector?.fullName || '');

                            // Lock fields that were populated from Brønnøysundregisteret
                            setLockedFields({
                              orgNumber: !!company.organizationNumber,
                              address: !!(company.visitingAddress || company.businessAddress)
                            });

                            // Reset address override when new company is selected
                            setUseCustomAddress(false);

                            // Mark that a company has been selected
                            setCompanySelected(true);
                          }}
                          error={errors.customerName}
                        />
                      ) : (
                        <TextInput
                          label={getCustomerNameLabel()}
                          placeholder={getCustomerNamePlaceholder()}
                          required
                          fullWidth
                          size="large"
                          value={formData.customerName}
                          onChange={(e) => setFormData(prev => ({ ...prev, customerName: e.target.value }))}
                          error={errors.customerName}
                        />
                      )}

                      {/* Organization Number (for bedrift, directly under company name) */}
                      {formData.customerType === 'bedrift' && (
                        lockedFields.orgNumber ? (
                          <LockedInput
                            label="Organisasjonsnummer"
                            value={formData.orgNumber}
                            fullWidth
                            size="large"
                          />
                        ) : (
                          <TextInput
                            label="Organisasjonsnummer"
                            placeholder={companySelected ? "F.eks. *********" : "Velg bedrift først"}
                            fullWidth
                            size="large"
                            value={formData.orgNumber}
                            onChange={(e) => setFormData(prev => ({ ...prev, orgNumber: e.target.value }))}
                            error={errors.orgNumber}
                            helperText={companySelected ? "9-sifret organisasjonsnummer" : "Organisasjonsnummer fylles automatisk når du velger bedrift"}
                            disabled={!companySelected}
                          />
                        )
                      )}

                      {/* Project Address with Override Toggle */}
                      <div className="space-y-4">
                        {/* Address Override Toggle (only show if we have Brreg data) */}
                        {lockedFields.address && brregData && (
                          <ToggleSwitch
                            label="Bruk annen prosjektadresse"
                            checked={useCustomAddress}
                            onChange={setUseCustomAddress}
                            helperText="Aktiver for å bruke en annen adresse enn bedriftens registrerte adresse"
                          />
                        )}

                        {/* Address Input - Locked or Editable */}
                        {lockedFields.address && !useCustomAddress ? (
                          <LockedInput
                            label="Prosjektadresse (Bedriftsadresse)"
                            value={formData.address}
                            fullWidth
                            size="large"
                          />
                        ) : (
                          <TextInput
                            label={lockedFields.address && useCustomAddress ? "Prosjektadresse (Tilpasset)" : "Prosjektadresse"}
                            placeholder={companySelected || formData.customerType === 'privat' ? "F.eks. Storgata 15, 0123 Oslo" : "Velg bedrift først"}
                            required
                            fullWidth
                            size="large"
                            value={formData.address}
                            onChange={(e) => setFormData(prev => ({ ...prev, address: e.target.value }))}
                            error={errors.address}
                            helperText={companySelected || formData.customerType === 'privat' ? "Adressen hvor arbeidet skal utføres" : "Adresse fylles automatisk når du velger bedrift"}
                            disabled={formData.customerType === 'bedrift' && !companySelected}
                          />
                        )}
                      </div>
                    </div>

                    {/* Section 2: Business Information (Only for bedrift) */}
                    {formData.customerType === 'bedrift' && (
                      <div className="space-y-4">
                        <div className="pb-2 border-b border-jobblogg-border">
                          <Heading3 className="text-lg font-semibold text-jobblogg-text-strong">
                            Bedriftsinformasjon
                          </Heading3>
                          <TextMuted className="text-sm">Valgfri informasjon om bedriften</TextMuted>
                        </div>

                        {/* Managing Director Reference (Read-only) */}
                        {managingDirectorInfo && (
                          <div className="bg-jobblogg-background-soft border border-jobblogg-border rounded-lg p-4">
                            <div className="flex items-center gap-2 mb-2">
                              <svg className="w-4 h-4 text-jobblogg-text-muted" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                              </svg>
                              <span className="text-sm font-medium text-jobblogg-text-strong">Bedriftsinformasjon</span>
                            </div>
                            <p className="text-sm text-jobblogg-text-muted">
                              <span className="font-medium">Daglig leder:</span> {managingDirectorInfo}
                            </p>
                          </div>
                        )}
                      </div>
                    )}

                    {/* Section 3: Contact Information */}
                    <div className="space-y-4">
                      <div className="pb-2 border-b border-jobblogg-border">
                        <Heading3 className="text-lg font-semibold text-jobblogg-text-strong">
                          Kontaktinformasjon
                        </Heading3>
                        <TextMuted className="text-sm">Kontaktinformasjon for kommunikasjon (påkrevd)</TextMuted>
                      </div>

                      {/* Contact Person */}
                      <TextInput
                        label="Kontaktperson"
                        placeholder="F.eks. Ola Nordmann"
                        fullWidth
                        value={formData.contactPerson}
                        onChange={(e) => setFormData(prev => ({ ...prev, contactPerson: e.target.value }))}
                        helperText="Navn på kontaktperson for dette prosjektet"
                      />

                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <TextInput
                          label={formData.customerType === 'bedrift' ? "Telefon (Kontaktperson)" : "Telefonnummer"}
                          placeholder="F.eks. 12345678"
                          required
                          fullWidth
                          value={formData.phone}
                          onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
                          error={errors.phone}
                          helperText={formData.customerType === 'bedrift' ? "Telefonnummer til kontaktperson (påkrevd)" : "For rask kontakt (påkrevd)"}
                        />
                        <TextInput
                          label={formData.customerType === 'bedrift' ? "E-post (Kontaktperson)" : "E-postadresse"}
                          type="email"
                          placeholder="F.eks. <EMAIL>"
                          required
                          fullWidth
                          value={formData.email}
                          onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                          error={errors.email}
                          helperText={formData.customerType === 'bedrift' ? "E-postadresse til kontaktperson (påkrevd)" : "For rapporter og varsling (påkrevd)"}
                        />
                      </div>
                    </div>

                    {/* Section 4: Additional Information */}
                    <div className="space-y-4">
                      <div className="pb-2 border-b border-jobblogg-border">
                        <Heading3 className="text-lg font-semibold text-jobblogg-text-strong">
                          Tilleggsinformasjon
                        </Heading3>
                        <TextMuted className="text-sm">Valgfri notater og spesielle instruksjoner</TextMuted>
                      </div>

                      <TextArea
                        label="Notater"
                        placeholder="F.eks. portkode, tilgjengelighet, spesielle ønsker..."
                        fullWidth
                        rows={4}
                        value={formData.notes}
                        onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                        helperText="Notater som kan være nyttige under prosjektet"
                      />
                    </div>
                  </div>
                )}
              </div>

              {/* Form Actions */}
              <div className="mt-12">
                <SubmitButton
                  size="large"
                  fullWidth
                  loading={isLoading}
                  loadingText="Oppretter prosjekt..."
                  disabled={isLoading}
                >
                  Opprett prosjekt med kunde
                </SubmitButton>
              </div>
            </form>
          </div>
        </div>

        {/* Additional Info */}
        <div className="mt-8 max-w-2xl mx-auto">
          <div className="bg-jobblogg-neutral rounded-xl p-6 text-center">
            <div className="flex items-center justify-center gap-6">
              <div className="flex items-center gap-2">
                <svg className="w-4 h-4 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <TextMuted>* Obligatoriske felt</TextMuted>
              </div>
              <div className="flex items-center gap-2">
                <svg className="w-4 h-4 text-jobblogg-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
                <TextMuted>Kan redigeres senere</TextMuted>
              </div>
              <div className="flex items-center gap-2">
                <svg className="w-4 h-4 text-info" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                </svg>
                <TextMuted>Sikker lagring</TextMuted>
              </div>
            </div>
          </div>
        </div>
    </PageLayout>
  );
};

export default CreateProject;
