import React, { useEffect, useRef, useState, useMemo } from 'react';
import { useMutation } from 'convex/react';
import { api } from '../../../../convex/_generated/api';
import { TextInput, SelectInput, PhoneInput, PrimaryButton, FormError, AddressMapPreview, AddressAutocomplete, PostalCodeInput, CompanyLookup, CompanyLookupRef, TextMuted, LockedInput, ToggleSwitch, TextArea } from '../../../components/ui';
import type { AddressSuggestion } from '../../../components/ui';
import { CompanyInfo } from '../../../services/companyLookup';

// Local interface definition to avoid import issues
interface WizardFormData {
  projectName: string;
  description: string;
  customerName: string;
  customerType: 'privat' | 'bedrift';
  contactPerson: string;
  phone: string;
  email: string;
  address: string;
  streetAddress: string;
  postalCode: string;
  city: string;
  entrance: string;
  orgNumber: string;
  notes: string;
  jobDescription: string;
  accessNotes: string;
  equipmentNeeds: string;
  unresolvedQuestions: string;
  personalNotes: string;
}

interface Step2CustomerInfoProps {
  formData: WizardFormData;
  updateFormData: (updates: Partial<WizardFormData>) => void;
  errors: { [key: string]: string };
  useExistingCustomer: boolean;
  setUseExistingCustomer: (value: boolean) => void;
  selectedCustomerId: string;
  setSelectedCustomerId: (value: string) => void;
  existingCustomers: any[] | undefined;
  onNext: () => void;
  onPrevious: () => void;
  isLoading: boolean;
  setIsLoading: (value: boolean) => void;
  setErrors: (errors: { [key: string]: string }) => void;
  createProject: any;
  createCustomer: any;
  user: any;
  clearSavedData: () => void;
  setShowSuccess: (value: boolean) => void;
  navigate: (path: string) => void;
  setCreatedProjectId: (projectId: string | null) => void;
  // Brønnøysundregisteret data tracking
  brregData: any;
  setBrregData: (data: any) => void;
  brregFetchedAt: number | null;
  setBrregFetchedAt: (timestamp: number | null) => void;
  useCustomAddress: boolean;
  setUseCustomAddress: (value: boolean) => void;
  lockedFields: {
    orgNumber: boolean;
    address: boolean;
  };
  setLockedFields: (fields: any) => void;
  companySelected: boolean;
  setCompanySelected: (value: boolean) => void;
  managingDirectorInfo: string;
  setManagingDirectorInfo: (value: string) => void;
}

export const Step2CustomerInfo: React.FC<Step2CustomerInfoProps> = ({
  formData,
  updateFormData,
  errors,
  useExistingCustomer,
  setUseExistingCustomer,
  selectedCustomerId,
  setSelectedCustomerId,
  existingCustomers,
  onNext,
  onPrevious,
  isLoading,
  setIsLoading,
  setErrors,
  createProject,
  createCustomer,
  user,
  clearSavedData,
  setShowSuccess,
  navigate,
  setCreatedProjectId,
  // Brønnøysundregisteret data tracking
  brregData,
  setBrregData,
  brregFetchedAt,
  setBrregFetchedAt,
  useCustomAddress,
  setUseCustomAddress,
  lockedFields,
  setLockedFields,
  companySelected,
  setCompanySelected,
  managingDirectorInfo,
  setManagingDirectorInfo
}) => {
  const companyLookupRef = useRef<CompanyLookupRef>(null);
  // Initialize with current formData.customerType to prevent incorrect reset on component mount
  const previousCustomerTypeRef = useRef<'privat' | 'bedrift'>(formData.customerType);

  // Filter existing customers based on selected customer type
  const filteredExistingCustomers = existingCustomers?.filter(customer => {
    // Handle both old 'firma' and new 'bedrift' values for backward compatibility
    const customerType = customer.type === 'firma' ? 'bedrift' : customer.type;
    return customerType === formData.customerType;
  }) || [];

  // Reset form when customer type changes to prevent data contamination
  useEffect(() => {
    // Only reset if customer type actually changed (not on initial load or form updates)
    if (previousCustomerTypeRef.current !== formData.customerType) {

      // Reset all customer-related form fields except customerType and project info
      updateFormData({
        customerName: '',
        contactPerson: '',
        phone: '',
        email: '',
        address: '',
        streetAddress: '',
        postalCode: '',
        city: '',
        entrance: '',
        orgNumber: ''
      });

      // Clear validation errors
      setErrors({});

      // Reset company lookup component
      if (companyLookupRef.current) {
        companyLookupRef.current.reset();
      }

      // Reset existing customer selection
      setUseExistingCustomer(false);
      setSelectedCustomerId('');

      // Reset company selection state
      setCompanySelected(false);
      setBrregData(null);
      setBrregFetchedAt(null);
      setUseCustomAddress(false);
      setLockedFields({
        orgNumber: false,
        address: false
      });
      setManagingDirectorInfo('');

      // Update the ref to track the new customer type
      previousCustomerTypeRef.current = formData.customerType;
    }
  }, [formData.customerType, updateFormData, setErrors, setUseExistingCustomer, setSelectedCustomerId]);

  // Load existing customer data into form when selected
  useEffect(() => {
    if (useExistingCustomer && selectedCustomerId && existingCustomers) {
      const selectedCustomer = existingCustomers.find(c => c._id === selectedCustomerId);
      if (selectedCustomer) {
        // Update form data with ONLY customer-level information
        // Project-specific data (contact person, phone, email, entrance, notes) should start empty for each new project
        updateFormData({
          // Customer-level data (identity and business registration)
          customerName: selectedCustomer.name || '',
          customerType: selectedCustomer.type === 'firma' ? 'bedrift' : selectedCustomer.type, // Handle legacy 'firma' type
          orgNumber: selectedCustomer.orgNumber || '',

          // Address data - use business address as default but allow project-specific override
          streetAddress: selectedCustomer.streetAddress || '',
          postalCode: selectedCustomer.postalCode || '',
          city: selectedCustomer.city || '',
          address: selectedCustomer.address || '', // Keep legacy address as fallback

          // Project-specific data - START EMPTY for each new project
          contactPerson: '', // Each project can have different contact person
          phone: '', // Each project can have different contact phone
          email: '', // Each project can have different contact email
          entrance: '', // Each project can have different entrance/floor details
          notes: '' // Each project can have different notes
        });

        // Handle Brønnøysundregisteret data for business customers
        if (selectedCustomer.type === 'bedrift' && selectedCustomer.brregData) {
          setBrregData(selectedCustomer.brregData);
          setBrregFetchedAt(selectedCustomer.brregFetchedAt || null);

          // Set managing director info
          if (selectedCustomer.brregData.managingDirector) {
            setManagingDirectorInfo(getManagingDirectorName(selectedCustomer.brregData.managingDirector));
          }

          // Set locked fields based on available Brønnøysundregisteret data
          setLockedFields({
            orgNumber: !!selectedCustomer.orgNumber,
            address: !!(selectedCustomer.brregData && (selectedCustomer.streetAddress && selectedCustomer.postalCode && selectedCustomer.city))
          });

          // Set company as selected if we have Brønnøysundregisteret data
          setCompanySelected(true);

          // Check if custom address is being used
          setUseCustomAddress(selectedCustomer.useCustomAddress || false);
        } else {
          // Reset business-specific state for private customers
          setBrregData(null);
          setBrregFetchedAt(null);
          setManagingDirectorInfo('');
          setLockedFields({ orgNumber: false, address: false });
          setCompanySelected(false);
          setUseCustomAddress(false);
        }
      }
    }
  }, [useExistingCustomer, selectedCustomerId, existingCustomers, updateFormData, setBrregData, setBrregFetchedAt, setManagingDirectorInfo, setLockedFields, setCompanySelected, setUseCustomAddress]);

  // Duplicate detection state
  const [duplicateCustomer, setDuplicateCustomer] = useState<any>(null);
  const [showDuplicateWarning, setShowDuplicateWarning] = useState(false);

  // Brønnøysundregisteret update notification state
  const [updateNotification, setUpdateNotification] = useState<{
    show: boolean;
    changes: Array<{
      field: string;
      label: string;
      oldValue: string;
      newValue: string;
    }>;
    preserved: string[];
  }>({
    show: false,
    changes: [],
    preserved: []
  });

  // Debounced duplicate detection for customer names (private customers)
  const duplicateNameCheck = useMemo(() => {
    if (!existingCustomers || useExistingCustomer || formData.customerType !== 'privat') {
      return null;
    }

    const trimmedName = formData.customerName.trim();
    if (trimmedName.length < 2) return null;

    // Case-insensitive search for duplicate customer names (private customers only)
    const duplicate = existingCustomers.find(customer =>
      customer.type === 'privat' &&
      customer.name.toLowerCase() === trimmedName.toLowerCase()
    );

    return duplicate || null;
  }, [existingCustomers, formData.customerName, formData.customerType, useExistingCustomer]);

  // Debounced duplicate detection for organization numbers (business customers)
  const duplicateOrgNumberCheck = useMemo(() => {
    if (!existingCustomers || useExistingCustomer || formData.customerType !== 'bedrift') {
      return null;
    }

    const trimmedOrgNumber = formData.orgNumber.trim();
    // Only check for duplicates when we have a complete 9-digit organization number
    if (trimmedOrgNumber.length !== 9 || !/^\d{9}$/.test(trimmedOrgNumber)) {
      return null;
    }

    // Search for duplicate organization numbers (business customers only)
    const duplicate = existingCustomers.find(customer =>
      (customer.type === 'bedrift' || customer.type === 'firma') &&
      customer.orgNumber === trimmedOrgNumber
    );

    return duplicate || null;
  }, [existingCustomers, formData.orgNumber, formData.customerType, useExistingCustomer]);

  // Update duplicate detection state
  useEffect(() => {
    const duplicate = formData.customerType === 'privat' ? duplicateNameCheck : duplicateOrgNumberCheck;
    setDuplicateCustomer(duplicate);
    setShowDuplicateWarning(!!duplicate);
  }, [duplicateNameCheck, duplicateOrgNumberCheck, formData.customerType]);

  // Clear duplicate warnings when switching customer types
  useEffect(() => {
    setDuplicateCustomer(null);
    setShowDuplicateWarning(false);
  }, [formData.customerType]);

  // Handle switching to existing customer mode when duplicate is detected
  const handleSelectExistingCustomer = (customer: any) => {
    setUseExistingCustomer(true);
    setSelectedCustomerId(customer._id);
    setShowDuplicateWarning(false);
    setDuplicateCustomer(null);
  };

  // Dynamic label and placeholder based on customer type
  const getCustomerNameLabel = () => {
    return formData.customerType === 'bedrift' ? 'Bedriftsnavn' : 'Kundenavn';
  };

  const getCustomerNamePlaceholder = () => {
    return formData.customerType === 'bedrift' ? 'F.eks. Nordmann Bygg AS' : 'F.eks. Ola Nordmann';
  };

  // Validate step 2 fields
  const validateStep = (): boolean => {
    const newErrors: { [key: string]: string } = {};

    if (!useExistingCustomer) {
      if (!formData.customerName || formData.customerName.trim().length < 2) {
        newErrors.customerName = 'Kundenavn må være minst 2 tegn langt';
      }

      // Validate individual address fields instead of combined address
      if (!formData.streetAddress || formData.streetAddress.trim().length < 3) {
        newErrors.streetAddress = 'Gateadresse må være minst 3 tegn lang';
      }

      if (!formData.postalCode || formData.postalCode.trim().length !== 4) {
        newErrors.postalCode = 'Postnummer må være 4 siffer';
      }

      if (!formData.city || formData.city.trim().length < 2) {
        newErrors.city = 'Poststed må være minst 2 tegn langt';
      }

      // Validate postal code format (4 digits)
      if (formData.postalCode && formData.postalCode.trim() !== '') {
        const postalCodePattern = /^\d{4}$/;
        if (!postalCodePattern.test(formData.postalCode.trim())) {
          newErrors.postalCode = 'Postnummer må være 4 siffer';
        }
      }

      if (formData.customerType === 'bedrift' && formData.orgNumber && formData.orgNumber.trim() !== '') {
        const orgNumberPattern = /^\d{9}$/;
        if (!orgNumberPattern.test(formData.orgNumber.trim())) {
          newErrors.orgNumber = 'Organisasjonsnummer må være 9 siffer';
        }
      }

      // Enhanced validation: Make phone and email required for both customer types
      if (formData.customerType === 'bedrift') {
        // Required fields for business customers
        if (!formData.phone || formData.phone.trim() === '') {
          newErrors.phone = 'Telefonnummer til kontaktperson er påkrevd';
        }
        if (!formData.email || formData.email.trim() === '') {
          newErrors.email = 'E-postadresse til kontaktperson er påkrevd';
        }
      } else {
        // Required fields for private customers
        if (!formData.phone || formData.phone.trim() === '') {
          newErrors.phone = 'Telefonnummer er påkrevd';
        }
        if (!formData.email || formData.email.trim() === '') {
          newErrors.email = 'E-postadresse er påkrevd';
        }
      }

      // Email format validation (if email is provided)
      if (formData.email && formData.email.trim() !== '') {
        const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailPattern.test(formData.email.trim())) {
          newErrors.email = 'Ugyldig e-postadresse';
        }
      }

      // Duplicate detection validation
      if (duplicateCustomer) {
        if (formData.customerType === 'privat') {
          newErrors.customerName = 'Denne kunden eksisterer allerede i din kundeliste';
        } else if (formData.customerType === 'bedrift') {
          newErrors.orgNumber = 'En bedrift med dette organisasjonsnummeret eksisterer allerede i din kundeliste';
        }
      }
    } else {
      if (!selectedCustomerId) {
        newErrors.selectedCustomer = 'Velg en eksisterende kunde';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validateStep()) {
      onNext();
    }
  };

  // Helper function to get managing director display name
  const getManagingDirectorName = (managingDirector: any): string => {
    if (typeof managingDirector === 'string') {
      return managingDirector;
    }
    if (typeof managingDirector === 'object' && managingDirector) {
      return managingDirector.fullName || `${managingDirector.firstName || ''} ${managingDirector.lastName || ''}`.trim();
    }
    return '';
  };

  // Handle updating customer information from Brønnøysundregisteret
  const updateCustomer = useMutation(api.customers.update);

  const handleUpdateFromBrreg = async (customer: any) => {
    if (!customer.orgNumber) {
      console.warn('No organization number found for customer');
      return;
    }

    try {
      setIsLoading(true);

      // Store original values for comparison
      const originalData = {
        name: customer.name,
        orgNumber: customer.orgNumber,
        managingDirector: customer.brregData?.managingDirector ? getManagingDirectorName(customer.brregData.managingDirector) : '',
        businessAddress: customer.brregData?.businessAddress ?
          `${customer.brregData.businessAddress.street || ''}, ${customer.brregData.businessAddress.postalCode || ''} ${customer.brregData.businessAddress.city || ''}`.trim() : '',
        visitingAddress: customer.brregData?.visitingAddress ?
          `${customer.brregData.visitingAddress.street || ''}, ${customer.brregData.visitingAddress.postalCode || ''} ${customer.brregData.visitingAddress.city || ''}`.trim() : '',
        industryDescription: customer.brregData?.industryDescription || '',
        status: customer.brregData?.status || ''
      };

      // Fetch fresh data from Brønnøysundregisteret
      const response = await fetch(`https://data.brreg.no/enhetsregisteret/api/enheter/${customer.orgNumber}`, {
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'JobbLogg/1.0 (Customer Update)'
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch company data: ${response.status}`);
      }

      const enhet = await response.json();

      // Fetch managing director information from roles API
      let managingDirector = null;
      try {
        const rolesResponse = await fetch(`https://data.brreg.no/enhetsregisteret/api/enheter/${customer.orgNumber}/roller`, {
          headers: {
            'Accept': 'application/json',
            'User-Agent': 'JobbLogg/1.0 (Customer Update)'
          }
        });

        if (rolesResponse.ok) {
          const rolesData = await rolesResponse.json();
          const managingDirectorGroup = rolesData.rollegrupper?.find((group: any) =>
            group.type?.kode === 'DAGL'
          );

          if (managingDirectorGroup?.roller?.[0]?.person) {
            const person = managingDirectorGroup.roller[0].person;
            const navn = person.navn;

            managingDirector = {
              firstName: navn.fornavn || '',
              lastName: navn.etternavn || '',
              fullName: [navn.fornavn, navn.mellomnavn, navn.etternavn].filter(Boolean).join(' '),
              birthDate: person.fodselsdato
            };
          }
        }
      } catch (error) {
        console.warn('Failed to fetch managing director:', error);
      }

      // Map the response to our company format
      const updatedBrregData = {
        name: enhet.navn,
        orgNumber: enhet.organisasjonsnummer,
        organizationNumber: enhet.organisasjonsnummer, // Add this for consistency
        status: enhet.organisasjonsform?.beskrivelse || '',
        industryCode: enhet.naeringskode1?.kode || '',
        industryDescription: enhet.naeringskode1?.beskrivelse || '',
        managingDirector,
        visitingAddress: null as any,
        businessAddress: null as any
      };

      // Map visiting address
      if (enhet.beliggenhetsadresse) {
        const addr = enhet.beliggenhetsadresse;
        updatedBrregData.visitingAddress = {
          street: [addr.adresse?.[0], addr.adresse?.[1]].filter(Boolean).join(' ').trim(),
          postalCode: addr.postnummer || '',
          city: addr.poststed || '',
          municipality: addr.kommune
        };
      }

      // Map business address
      if (enhet.forretningsadresse) {
        const addr = enhet.forretningsadresse;
        updatedBrregData.businessAddress = {
          street: [addr.adresse?.[0], addr.adresse?.[1]].filter(Boolean).join(' ').trim(),
          postalCode: addr.postnummer || '',
          city: addr.poststed || '',
          municipality: addr.kommune
        };
      }

      // Compare and track changes
      const changes: Array<{
        field: string;
        label: string;
        oldValue: string;
        newValue: string;
      }> = [];

      // Check company name changes
      if (originalData.name !== updatedBrregData.name) {
        changes.push({
          field: 'name',
          label: 'Bedriftsnavn',
          oldValue: originalData.name,
          newValue: updatedBrregData.name
        });
      }

      // Check organization number changes
      if (originalData.orgNumber !== updatedBrregData.orgNumber) {
        changes.push({
          field: 'orgNumber',
          label: 'Organisasjonsnummer',
          oldValue: originalData.orgNumber,
          newValue: updatedBrregData.orgNumber
        });
      }

      // Check managing director changes
      const newManagingDirector = managingDirector ? getManagingDirectorName(managingDirector) : '';
      if (originalData.managingDirector !== newManagingDirector) {
        changes.push({
          field: 'managingDirector',
          label: 'Daglig leder',
          oldValue: originalData.managingDirector || 'Ikke registrert',
          newValue: newManagingDirector || 'Ikke registrert'
        });
      }

      // Check business address changes
      const newBusinessAddress = updatedBrregData.businessAddress ?
        `${updatedBrregData.businessAddress.street || ''}, ${updatedBrregData.businessAddress.postalCode || ''} ${updatedBrregData.businessAddress.city || ''}`.trim() : '';
      if (originalData.businessAddress !== newBusinessAddress) {
        changes.push({
          field: 'businessAddress',
          label: 'Forretningsadresse',
          oldValue: originalData.businessAddress || 'Ikke registrert',
          newValue: newBusinessAddress || 'Ikke registrert'
        });
      }

      // Check visiting address changes
      const newVisitingAddress = updatedBrregData.visitingAddress ?
        `${updatedBrregData.visitingAddress.street || ''}, ${updatedBrregData.visitingAddress.postalCode || ''} ${updatedBrregData.visitingAddress.city || ''}`.trim() : '';
      if (originalData.visitingAddress !== newVisitingAddress) {
        changes.push({
          field: 'visitingAddress',
          label: 'Besøksadresse',
          oldValue: originalData.visitingAddress || 'Ikke registrert',
          newValue: newVisitingAddress || 'Ikke registrert'
        });
      }

      // Check industry description changes
      if (originalData.industryDescription !== updatedBrregData.industryDescription) {
        changes.push({
          field: 'industryDescription',
          label: 'Bransje',
          oldValue: originalData.industryDescription || 'Ikke registrert',
          newValue: updatedBrregData.industryDescription || 'Ikke registrert'
        });
      }

      // Check status changes
      if (originalData.status !== updatedBrregData.status) {
        changes.push({
          field: 'status',
          label: 'Organisasjonsform',
          oldValue: originalData.status || 'Ikke registrert',
          newValue: updatedBrregData.status || 'Ikke registrert'
        });
      }

      // Update the customer record with fresh data
      await updateCustomer({
        customerId: customer._id,
        userId: user?.id || '',
        brregData: updatedBrregData,
        brregFetchedAt: Date.now()
      });

      // Update local state with new Brønnøysundregisteret data
      setBrregData(updatedBrregData);
      setBrregFetchedAt(Date.now());

      // Update managing director info
      if (managingDirector) {
        setManagingDirectorInfo(getManagingDirectorName(managingDirector));
      }

      // Update form data if address was changed and not using custom address
      if (!useCustomAddress && updatedBrregData.visitingAddress) {
        updateFormData({
          streetAddress: updatedBrregData.visitingAddress.street || '',
          postalCode: updatedBrregData.visitingAddress.postalCode || '',
          city: updatedBrregData.visitingAddress.city || ''
        });
      } else if (!useCustomAddress && updatedBrregData.businessAddress) {
        updateFormData({
          streetAddress: updatedBrregData.businessAddress.street || '',
          postalCode: updatedBrregData.businessAddress.postalCode || '',
          city: updatedBrregData.businessAddress.city || ''
        });
      }

      // Show update notification
      setUpdateNotification({
        show: true,
        changes,
        preserved: [
          'Kontaktperson',
          'Telefonnummer',
          'E-postadresse',
          'Tilpasset prosjektadresse',
          'Oppgang/Inngang/Etasje',
          'Notater'
        ]
      });

      console.log('✅ Customer information updated from Brønnøysundregisteret');

    } catch (error) {
      console.error('❌ Failed to update customer from Brønnøysundregisteret:', error);
      setErrors({ brregUpdate: 'Kunne ikke oppdatere kundeinformasjon fra Brønnøysundregisteret' });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Customer Selection Toggle */}
      <div className="bg-jobblogg-neutral rounded-lg p-4">
        <div className="flex items-center space-x-4">
          <label className="flex items-center">
            <input
              type="radio"
              name="customerChoice"
              checked={!useExistingCustomer}
              onChange={() => setUseExistingCustomer(false)}
              className="w-4 h-4 text-jobblogg-primary border-jobblogg-border focus:ring-jobblogg-primary/30"
            />
            <span className="ml-2 text-sm font-medium text-jobblogg-text-strong">Ny kunde</span>
          </label>
          <label className="flex items-center">
            <input
              type="radio"
              name="customerChoice"
              checked={useExistingCustomer}
              onChange={() => setUseExistingCustomer(true)}
              className="w-4 h-4 text-jobblogg-primary border-jobblogg-border focus:ring-jobblogg-primary/30"
            />
            <span className="ml-2 text-sm font-medium text-jobblogg-text-strong">Eksisterende kunde</span>
          </label>
        </div>
      </div>

      {/* Existing Customer Selection */}
      {useExistingCustomer && (
        <div className="space-y-4">
          {filteredExistingCustomers.length > 0 ? (
            <>
              <SelectInput
                label="Velg kunde"
                placeholder={`Velg en eksisterende ${formData.customerType === 'bedrift' ? 'bedrift' : 'privatkunde'}`}
                required
                fullWidth
                value={selectedCustomerId}
                onChange={(e) => setSelectedCustomerId(e.target.value)}
                error={errors.selectedCustomer}
                helperText={`Velg fra dine eksisterende ${formData.customerType === 'bedrift' ? 'bedrifter' : 'privatkunder'}`}
                options={filteredExistingCustomers.map((customer) => {
                  // Use structured address fields with fallback to legacy address
                  let displayAddress = 'Ingen adresse';
                  if (customer.streetAddress && customer.postalCode && customer.city) {
                    displayAddress = `${customer.streetAddress}, ${customer.postalCode} ${customer.city}`;
                  } else if (customer.address) {
                    displayAddress = customer.address;
                  }

                  return {
                    value: customer._id,
                    label: `${customer.name} - ${displayAddress}`
                  };
                })}
              />

              {/* Selected Customer Editable Form */}
              {selectedCustomerId && (() => {
                const selectedCustomer = filteredExistingCustomers.find(c => c._id === selectedCustomerId);
                if (!selectedCustomer) return null;

                return (
                  <div className="space-y-6">
                    {/* Header with Update Button */}
                    <div className="flex items-center justify-between">
                      <h4 className="font-semibold text-jobblogg-text-strong">Rediger kundeinformasjon</h4>
                      {selectedCustomer.type === 'bedrift' && selectedCustomer.brregData && (
                        <PrimaryButton
                          size="sm"
                          onClick={() => handleUpdateFromBrreg(selectedCustomer)}
                          className="text-xs"
                        >
                          Oppdater fra Brønnøysundregisteret
                        </PrimaryButton>
                      )}
                    </div>

                    {/* Customer Name with Company Lookup for Bedrift */}
                    {formData.customerType === 'bedrift' ? (
                      // For existing business customers, lock the company name field
                      useExistingCustomer ? (
                        <LockedInput
                          label="Bedriftsnavn"
                          value={formData.customerName}
                          fullWidth
                          helperText="Bedriftsnavn kan ikke endres for eksisterende kunder"
                        />
                      ) : (
                        // For new business customers, use CompanyLookup
                        <CompanyLookup
                          ref={companyLookupRef}
                          companyName={formData.customerName}
                          onCompanyNameChange={(name) => updateFormData({ customerName: name })}
                          onCompanySelect={(company: CompanyInfo) => {
                            // Store Brønnøysundregisteret data
                            const brregTimestamp = Date.now();
                            setBrregData(company);
                            setBrregFetchedAt(brregTimestamp);

                            // Auto-fill form with company information
                            // Use visiting address if available, otherwise business address
                            const address = company.visitingAddress || company.businessAddress;
                            updateFormData({
                              customerName: company.name,
                              orgNumber: company.organizationNumber,
                              streetAddress: address?.street || '',
                              postalCode: address?.postalCode || '',
                              city: address?.city || ''
                            });

                            // Store managing director as reference information (not editable)
                            setManagingDirectorInfo(company.managingDirector?.fullName || '');

                            // Lock fields that were populated from Brønnøysundregisteret
                            setLockedFields({
                              orgNumber: !!company.organizationNumber,
                              address: !!(company.visitingAddress || company.businessAddress)
                            });

                            // Reset address override when new company is selected
                            setUseCustomAddress(false);

                            // Mark that a company has been selected
                            setCompanySelected(true);
                          }}
                          error={errors.customerName}
                        />
                      )
                    ) : (
                      // For private customers, always use regular TextInput
                      <TextInput
                        label={getCustomerNameLabel()}
                        placeholder={getCustomerNamePlaceholder()}
                        required
                        fullWidth
                        value={formData.customerName}
                        onChange={(e) => updateFormData({ customerName: e.target.value })}
                        error={errors.customerName}
                      />
                    )}

                    {/* Organization Number (for bedrift, directly under company name) */}
                    {formData.customerType === 'bedrift' && (
                      lockedFields.orgNumber ? (
                        <LockedInput
                          label="Organisasjonsnummer"
                          value={formData.orgNumber}
                          fullWidth
                        />
                      ) : (
                        <TextInput
                          label="Organisasjonsnummer"
                          placeholder={companySelected ? "F.eks. *********" : "Velg bedrift først"}
                          fullWidth
                          value={formData.orgNumber}
                          onChange={(e) => updateFormData({ orgNumber: e.target.value })}
                          error={errors.orgNumber}
                          helperText={companySelected ? "9-sifret organisasjonsnummer" : "Organisasjonsnummer fylles automatisk når du velger bedrift"}
                          disabled={!companySelected}
                        />
                      )
                    )}

                    {/* Managing Director Reference (Read-only) */}
                    {formData.customerType === 'bedrift' && managingDirectorInfo && (
                      <div className="bg-jobblogg-background-soft border border-jobblogg-border rounded-lg p-4">
                        <div className="flex items-center gap-2 mb-2">
                          <svg className="w-4 h-4 text-jobblogg-text-muted" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                          <span className="text-sm font-medium text-jobblogg-text-strong">Bedriftsinformasjon</span>
                        </div>
                        <p className="text-sm text-jobblogg-text-muted">
                          <span className="font-medium">Daglig leder:</span> {managingDirectorInfo}
                        </p>
                      </div>
                    )}

                    {/* Address Override Toggle and Smart Address Fields */}
                    <div className="space-y-4">
                      {/* Address Override Toggle (only show if we have Brreg data) */}
                      {lockedFields.address && brregData && (
                        <ToggleSwitch
                          label="Bruk annen prosjektadresse"
                          checked={useCustomAddress}
                          onChange={setUseCustomAddress}
                          helperText="Aktiver for å bruke en annen adresse enn bedriftens registrerte adresse"
                        />
                      )}

                      {/* Street Address - Locked or Editable */}
                      {lockedFields.address && !useCustomAddress ? (
                        <LockedInput
                          label="Gateadresse (Bedriftsadresse)"
                          value={formData.streetAddress}
                          fullWidth
                        />
                      ) : (
                        <AddressAutocomplete
                          label={lockedFields.address && useCustomAddress ? "Gateadresse (Tilpasset)" : "Gateadresse"}
                          placeholder={companySelected || formData.customerType === 'privat' ? "F.eks. Storgata 15" : "Velg bedrift først"}
                          required
                          fullWidth
                          value={formData.streetAddress}
                          onChange={(value) => updateFormData({ streetAddress: value })}
                          onAddressSelect={(suggestion: AddressSuggestion) => {
                            // Auto-fill postal code and city when address is selected
                            updateFormData({
                              streetAddress: suggestion.address,
                              postalCode: suggestion.postalCode,
                              city: suggestion.city
                            });
                          }}
                          error={errors.streetAddress}
                          helperText={companySelected || formData.customerType === 'privat' ? "Start å skrive for å få forslag til adresser" : "Adresse fylles automatisk når du velger bedrift"}
                          disabled={formData.customerType === 'bedrift' && !companySelected}
                        />
                      )}

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {/* Postal Code - Locked or Editable */}
                        {lockedFields.address && !useCustomAddress ? (
                          <LockedInput
                            label="Postnummer (Bedriftsadresse)"
                            value={formData.postalCode}
                            fullWidth
                          />
                        ) : (
                          <PostalCodeInput
                            label={lockedFields.address && useCustomAddress ? "Postnummer (Tilpasset)" : "Postnummer"}
                            placeholder={companySelected || formData.customerType === 'privat' ? "F.eks. 0123" : "Velg bedrift først"}
                            required
                            fullWidth
                            value={formData.postalCode}
                            onChange={(value) => updateFormData({ postalCode: value })}
                            onCityChange={(city) => updateFormData({ city })}
                            error={errors.postalCode}
                            helperText={companySelected || formData.customerType === 'privat' ? "4 siffer" : "Postnummer fylles automatisk når du velger bedrift"}
                            disabled={formData.customerType === 'bedrift' && !companySelected}
                          />
                        )}

                        {/* City - Locked or Editable */}
                        {lockedFields.address && !useCustomAddress ? (
                          <LockedInput
                            label="Poststed (Bedriftsadresse)"
                            value={formData.city}
                            fullWidth
                          />
                        ) : (
                          <TextInput
                            label={lockedFields.address && useCustomAddress ? "Poststed (Tilpasset)" : "Poststed"}
                            placeholder={companySelected || formData.customerType === 'privat' ? "F.eks. Oslo" : "Velg bedrift først"}
                            required
                            fullWidth
                            value={formData.city}
                            onChange={(e) => updateFormData({ city: e.target.value })}
                            error={errors.city}
                            helperText={companySelected || formData.customerType === 'privat' ? "Fylles automatisk ut fra postnummer" : "Poststed fylles automatisk når du velger bedrift"}
                            disabled={formData.customerType === 'bedrift' && !companySelected}
                          />
                        )}
                      </div>

                      <TextInput
                        label="Oppgang/Inngang/Etasje"
                        placeholder="F.eks. Oppgang A, 2. etasje (valgfritt)"
                        fullWidth
                        value={formData.entrance}
                        onChange={(e) => updateFormData({ entrance: e.target.value })}
                        helperText="Prosjektspesifikke detaljer for å finne frem til arbeidsstedet"
                      />
                    </div>

                    {/* Address Map Preview */}
                    <AddressMapPreview
                      streetAddress={formData.streetAddress}
                      postalCode={formData.postalCode}
                      city={formData.city}
                      className="mt-4"
                      width={400}
                      height={200}
                      zoom={15}
                    />

                    {/* Project-Specific Contact Information Section */}
                    <div className="bg-jobblogg-background-soft border border-jobblogg-border rounded-lg p-4">
                      <div className="flex items-center gap-2 mb-4">
                        <svg className="w-5 h-5 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                        <h4 className="text-sm font-semibold text-jobblogg-text-strong">
                          Prosjektspesifikk kontaktinformasjon
                        </h4>
                      </div>
                      <p className="text-sm text-jobblogg-text-muted mb-4">
                        Denne informasjonen er unik for dette prosjektet og påvirker ikke andre prosjekter med samme kunde.
                      </p>

                    {/* Contact Person */}
                    <TextInput
                      label="Kontaktperson"
                      placeholder="F.eks. Ola Nordmann"
                      fullWidth
                      value={formData.contactPerson}
                      onChange={(e) => updateFormData({ contactPerson: e.target.value })}
                      helperText="Kontaktperson for dette spesifikke prosjektet (kan være forskjellig for hvert prosjekt)"
                    />

                    {/* Phone and Email - Now Required */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <PhoneInput
                        label={formData.customerType === 'bedrift' ? "Telefon (Kontaktperson)" : "Telefon"}
                        required
                        fullWidth
                        value={formData.phone}
                        onChange={(value) => updateFormData({ phone: value })}
                        error={errors.phone}
                        helperText={formData.customerType === 'bedrift' ? "Telefonnummer til kontaktperson for dette prosjektet (påkrevd)" : "Telefonnummer for dette prosjektet (påkrevd)"}
                      />
                      <TextInput
                        label={formData.customerType === 'bedrift' ? "E-post (Kontaktperson)" : "E-post"}
                        type="email"
                        placeholder="F.eks. <EMAIL>"
                        required
                        fullWidth
                        value={formData.email}
                        onChange={(e) => updateFormData({ email: e.target.value })}
                        error={errors.email}
                        helperText={formData.customerType === 'bedrift' ? "E-postadresse til kontaktperson for dette prosjektet (påkrevd)" : "E-postadresse for dette prosjektet (påkrevd)"}
                      />
                    </div>

                    {/* Notes Field */}
                    <TextArea
                      label="Notater"
                      placeholder="Prosjektspesifikke notater (valgfritt)"
                      fullWidth
                      rows={3}
                      value={formData.notes}
                      onChange={(e) => updateFormData({ notes: e.target.value })}
                      helperText="Notater spesifikt for dette prosjektet (f.eks. nøkkelkoder, allergier, spesielle instruksjoner)"
                    />
                    </div>
                  </div>
                );
              })()}
            </>
          ) : (
            <div className="p-4 bg-jobblogg-card-bg border border-jobblogg-border rounded-lg">
              <div className="text-center">
                <TextMuted className="text-sm">
                  Ingen {formData.customerType === 'bedrift' ? 'bedrifter' : 'privatkunder'} funnet.
                </TextMuted>
                <TextMuted className="text-xs mt-1">
                  Opprett en ny kunde nedenfor.
                </TextMuted>
              </div>
            </div>
          )}
        </div>
      )}

      {/* New Customer Form */}
      {!useExistingCustomer && (
        <>
          {/* Customer Type */}
          <SelectInput
            label="Kundetype"
            placeholder="Velg kundetype"
            required
            fullWidth
            value={formData.customerType}
            onChange={(e) => updateFormData({ customerType: e.target.value as 'privat' | 'bedrift' })}
            options={[
              { value: 'privat', label: 'Privat' },
              { value: 'bedrift', label: 'Bedrift' }
            ]}
          />

          {/* Customer Name with Company Lookup for Bedrift */}
          {formData.customerType === 'bedrift' ? (
            <CompanyLookup
              ref={companyLookupRef}
              companyName={formData.customerName}
              onCompanyNameChange={(name) => updateFormData({ customerName: name })}
              onCompanySelect={(company: CompanyInfo) => {
                // Store Brønnøysundregisteret data
                const brregTimestamp = Date.now();
                setBrregData(company);
                setBrregFetchedAt(brregTimestamp);

                // Auto-fill form with company information
                // Use visiting address if available, otherwise business address
                const address = company.visitingAddress || company.businessAddress;
                updateFormData({
                  customerName: company.name,
                  orgNumber: company.organizationNumber,
                  streetAddress: address?.street || '',
                  postalCode: address?.postalCode || '',
                  city: address?.city || ''
                });

                // Store managing director as reference information (not editable)
                setManagingDirectorInfo(company.managingDirector?.fullName || '');

                // Lock fields that were populated from Brønnøysundregisteret
                setLockedFields({
                  orgNumber: !!company.organizationNumber,
                  address: !!(company.visitingAddress || company.businessAddress)
                });

                // Reset address override when new company is selected
                setUseCustomAddress(false);

                // Mark that a company has been selected
                setCompanySelected(true);
              }}
              error={errors.customerName}
            />
          ) : (
            <TextInput
              label={getCustomerNameLabel()}
              placeholder={getCustomerNamePlaceholder()}
              required
              fullWidth
              value={formData.customerName}
              onChange={(e) => updateFormData({ customerName: e.target.value })}
              error={errors.customerName}
            />
          )}

          {/* Organization Number (for bedrift, directly under company name) */}
          {formData.customerType === 'bedrift' && (
            lockedFields.orgNumber ? (
              <LockedInput
                label="Organisasjonsnummer"
                value={formData.orgNumber}
                fullWidth
              />
            ) : (
              <TextInput
                label="Organisasjonsnummer"
                placeholder={companySelected ? "F.eks. *********" : "Velg bedrift først"}
                fullWidth
                value={formData.orgNumber}
                onChange={(e) => updateFormData({ orgNumber: e.target.value })}
                error={errors.orgNumber}
                helperText={companySelected ? "9-sifret organisasjonsnummer" : "Organisasjonsnummer fylles automatisk når du velger bedrift"}
                disabled={!companySelected}
              />
            )
          )}

          {/* Duplicate Customer Warning */}
          {showDuplicateWarning && duplicateCustomer && (
            <div className="bg-jobblogg-warning-soft border border-jobblogg-warning/30 rounded-lg p-4">
              <div className="flex items-start gap-3">
                <div className="flex-shrink-0">
                  <svg className="w-5 h-5 text-jobblogg-warning mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                  </svg>
                </div>
                <div className="flex-1 min-w-0">
                  <h4 className="text-sm font-semibold text-jobblogg-warning mb-2">
                    {formData.customerType === 'privat'
                      ? 'Denne kunden eksisterer allerede i din kundeliste'
                      : 'En bedrift med dette organisasjonsnummeret eksisterer allerede i din kundeliste'
                    }
                  </h4>

                  {/* Existing Customer Preview */}
                  <div className="bg-white rounded-lg p-3 border border-jobblogg-border mb-3">
                    <div className="text-sm space-y-1">
                      <div>
                        <span className="font-medium text-jobblogg-text-strong">
                          {duplicateCustomer.type === 'bedrift' ? 'Bedrift:' : 'Kunde:'}
                        </span>
                        <span className="ml-2 text-jobblogg-text-medium">{duplicateCustomer.name}</span>
                      </div>

                      {duplicateCustomer.type === 'bedrift' && duplicateCustomer.orgNumber && (
                        <div>
                          <span className="font-medium text-jobblogg-text-strong">Org.nr:</span>
                          <span className="ml-2 text-jobblogg-text-medium">{duplicateCustomer.orgNumber}</span>
                        </div>
                      )}

                      {duplicateCustomer.phone && (
                        <div>
                          <span className="font-medium text-jobblogg-text-strong">Telefon:</span>
                          <span className="ml-2 text-jobblogg-text-medium">{duplicateCustomer.phone}</span>
                        </div>
                      )}

                      {duplicateCustomer.email && (
                        <div>
                          <span className="font-medium text-jobblogg-text-strong">E-post:</span>
                          <span className="ml-2 text-jobblogg-text-medium">{duplicateCustomer.email}</span>
                        </div>
                      )}

                      {(duplicateCustomer.streetAddress || duplicateCustomer.address) && (
                        <div>
                          <span className="font-medium text-jobblogg-text-strong">Adresse:</span>
                          <span className="ml-2 text-jobblogg-text-medium">
                            {duplicateCustomer.streetAddress
                              ? `${duplicateCustomer.streetAddress}${duplicateCustomer.entrance ? `, ${duplicateCustomer.entrance}` : ''}, ${duplicateCustomer.postalCode} ${duplicateCustomer.city}`
                              : duplicateCustomer.address
                            }
                          </span>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Action Button */}
                  <PrimaryButton
                    size="sm"
                    onClick={() => handleSelectExistingCustomer(duplicateCustomer)}
                    className="w-full sm:w-auto"
                  >
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Velg eksisterende kunde
                  </PrimaryButton>
                </div>
              </div>
            </div>
          )}

          {/* Brønnøysundregisteret Update Notification */}
          {updateNotification.show && (
            <div className="bg-jobblogg-success-soft border border-jobblogg-success/30 rounded-lg p-4">
              <div className="flex items-start gap-3">
                <div className="flex-shrink-0">
                  <svg className="w-5 h-5 text-jobblogg-success mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="text-sm font-semibold text-jobblogg-success">
                      Kundeinformasjon oppdatert fra Brønnøysundregisteret
                    </h4>
                    <button
                      onClick={() => setUpdateNotification({ show: false, changes: [], preserved: [] })}
                      className="text-jobblogg-text-muted hover:text-jobblogg-text-strong transition-colors duration-200"
                      aria-label="Lukk melding"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  </div>

                  {updateNotification.changes.length > 0 ? (
                    <>
                      <div className="mb-4">
                        <h5 className="text-xs font-semibold text-jobblogg-text-strong mb-2 uppercase tracking-wide">
                          Oppdaterte felter ({updateNotification.changes.length})
                        </h5>
                        <div className="space-y-2">
                          {updateNotification.changes.map((change, index) => (
                            <div key={index} className="bg-white rounded-lg p-3 border border-jobblogg-border">
                              <div className="text-sm">
                                <div className="font-medium text-jobblogg-text-strong mb-1">
                                  {change.label}
                                </div>
                                <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 text-xs">
                                  <div>
                                    <span className="text-jobblogg-text-muted">Før:</span>
                                    <div className="text-jobblogg-text-medium bg-jobblogg-background-soft px-2 py-1 rounded mt-1">
                                      {change.oldValue || 'Ikke registrert'}
                                    </div>
                                  </div>
                                  <div>
                                    <span className="text-jobblogg-text-muted">Etter:</span>
                                    <div className="text-jobblogg-text-medium bg-jobblogg-success-soft px-2 py-1 rounded mt-1">
                                      {change.newValue || 'Ikke registrert'}
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    </>
                  ) : (
                    <div className="mb-4">
                      <p className="text-sm text-jobblogg-text-medium">
                        Ingen endringer funnet. All informasjon er oppdatert og stemmer overens med Brønnøysundregisteret.
                      </p>
                    </div>
                  )}

                  {updateNotification.preserved.length > 0 && (
                    <div className="bg-jobblogg-background-soft rounded-lg p-3 border border-jobblogg-border">
                      <h5 className="text-xs font-semibold text-jobblogg-text-strong mb-2 uppercase tracking-wide">
                        Bevarte brukerdata
                      </h5>
                      <p className="text-sm text-jobblogg-text-medium mb-2">
                        Følgende informasjon ble IKKE endret og beholder dine egne verdier:
                      </p>
                      <div className="flex flex-wrap gap-1">
                        {updateNotification.preserved.map((field, index) => (
                          <span
                            key={index}
                            className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-jobblogg-primary-soft text-jobblogg-primary"
                          >
                            {field}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Managing Director Reference (Read-only) */}
          {formData.customerType === 'bedrift' && managingDirectorInfo && (
            <div className="bg-jobblogg-background-soft border border-jobblogg-border rounded-lg p-4">
              <div className="flex items-center gap-2 mb-2">
                <svg className="w-4 h-4 text-jobblogg-text-muted" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span className="text-sm font-medium text-jobblogg-text-strong">Bedriftsinformasjon</span>
              </div>
              <p className="text-sm text-jobblogg-text-muted">
                <span className="font-medium">Daglig leder:</span> {managingDirectorInfo}
              </p>
            </div>
          )}

          {/* Address Override Toggle and Smart Address Fields */}
          <div className="space-y-4">
            {/* Address Override Toggle (only show if we have Brreg data) */}
            {lockedFields.address && brregData && (
              <ToggleSwitch
                label="Bruk annen prosjektadresse"
                checked={useCustomAddress}
                onChange={setUseCustomAddress}
                helperText="Aktiver for å bruke en annen adresse enn bedriftens registrerte adresse"
              />
            )}
            {/* Street Address - Locked or Editable */}
            {lockedFields.address && !useCustomAddress ? (
              <LockedInput
                label="Gateadresse (Bedriftsadresse)"
                value={formData.streetAddress}
                fullWidth
              />
            ) : (
              <AddressAutocomplete
                label={lockedFields.address && useCustomAddress ? "Gateadresse (Tilpasset)" : "Gateadresse"}
                placeholder={companySelected || formData.customerType === 'privat' ? "F.eks. Storgata 15" : "Velg bedrift først"}
                required
                fullWidth
                value={formData.streetAddress}
                onChange={(value) => updateFormData({ streetAddress: value })}
                onAddressSelect={(suggestion: AddressSuggestion) => {
                  // Auto-fill postal code and city when address is selected
                  updateFormData({
                    streetAddress: suggestion.address,
                    postalCode: suggestion.postalCode,
                    city: suggestion.city
                  });
                }}
                error={errors.streetAddress}
                helperText={companySelected || formData.customerType === 'privat' ? "Start å skrive for å få forslag til adresser" : "Adresse fylles automatisk når du velger bedrift"}
                disabled={formData.customerType === 'bedrift' && !companySelected}
              />
            )}

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Postal Code - Locked or Editable */}
              {lockedFields.address && !useCustomAddress ? (
                <LockedInput
                  label="Postnummer (Bedriftsadresse)"
                  value={formData.postalCode}
                  fullWidth
                />
              ) : (
                <PostalCodeInput
                  label={lockedFields.address && useCustomAddress ? "Postnummer (Tilpasset)" : "Postnummer"}
                  placeholder={companySelected || formData.customerType === 'privat' ? "F.eks. 0123" : "Velg bedrift først"}
                  required
                  fullWidth
                  value={formData.postalCode}
                  onChange={(value) => updateFormData({ postalCode: value })}
                  onCityChange={(city) => updateFormData({ city })}
                  error={errors.postalCode}
                  helperText={companySelected || formData.customerType === 'privat' ? "4 siffer" : "Postnummer fylles automatisk når du velger bedrift"}
                  disabled={formData.customerType === 'bedrift' && !companySelected}
                />
              )}

              {/* City - Locked or Editable */}
              {lockedFields.address && !useCustomAddress ? (
                <LockedInput
                  label="Poststed (Bedriftsadresse)"
                  value={formData.city}
                  fullWidth
                />
              ) : (
                <TextInput
                  label={lockedFields.address && useCustomAddress ? "Poststed (Tilpasset)" : "Poststed"}
                  placeholder={companySelected || formData.customerType === 'privat' ? "F.eks. Oslo" : "Velg bedrift først"}
                  required
                  fullWidth
                  value={formData.city}
                  onChange={(e) => updateFormData({ city: e.target.value })}
                  error={errors.city}
                  helperText={companySelected || formData.customerType === 'privat' ? "Fylles automatisk ut fra postnummer" : "Poststed fylles automatisk når du velger bedrift"}
                  disabled={formData.customerType === 'bedrift' && !companySelected}
                />
              )}
            </div>

            <TextInput
              label="Oppgang/Inngang/Etasje"
              placeholder="F.eks. Oppgang A, 2. etasje (valgfritt)"
              fullWidth
              value={formData.entrance}
              onChange={(e) => updateFormData({ entrance: e.target.value })}
              helperText="Prosjektspesifikke detaljer for å finne frem til arbeidsstedet"
            />
          </div>

          {/* Address Map Preview */}
          <AddressMapPreview
            streetAddress={formData.streetAddress}
            postalCode={formData.postalCode}
            city={formData.city}
            className="mt-4"
            width={400}
            height={200}
            zoom={15}
          />

          {/* Project-Specific Contact Information Section */}
          <div className="bg-jobblogg-background-soft border border-jobblogg-border rounded-lg p-4">
            <div className="flex items-center gap-2 mb-4">
              <svg className="w-5 h-5 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
              <h4 className="text-sm font-semibold text-jobblogg-text-strong">
                Prosjektspesifikk kontaktinformasjon
              </h4>
            </div>
            <p className="text-sm text-jobblogg-text-muted mb-4">
              Denne informasjonen er unik for dette prosjektet og påvirker ikke andre prosjekter med samme kunde.
            </p>

          {/* Contact Person */}
          <TextInput
            label="Kontaktperson"
            placeholder="F.eks. Ola Nordmann"
            fullWidth
            value={formData.contactPerson}
            onChange={(e) => updateFormData({ contactPerson: e.target.value })}
            helperText="Kontaktperson for dette spesifikke prosjektet (kan være forskjellig for hvert prosjekt)"
          />

          {/* Phone and Email - Now Required */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <PhoneInput
              label={formData.customerType === 'bedrift' ? "Telefon (Kontaktperson)" : "Telefon"}
              required
              fullWidth
              value={formData.phone}
              onChange={(value) => updateFormData({ phone: value })}
              error={errors.phone}
              helperText={formData.customerType === 'bedrift' ? "Telefonnummer til kontaktperson for dette prosjektet (påkrevd)" : "Telefonnummer for dette prosjektet (påkrevd)"}
            />
            <TextInput
              label={formData.customerType === 'bedrift' ? "E-post (Kontaktperson)" : "E-post"}
              type="email"
              placeholder="F.eks. <EMAIL>"
              required
              fullWidth
              value={formData.email}
              onChange={(e) => updateFormData({ email: e.target.value })}
              error={errors.email}
              helperText={formData.customerType === 'bedrift' ? "E-postadresse til kontaktperson for dette prosjektet (påkrevd)" : "E-postadresse for dette prosjektet (påkrevd)"}
            />
          </div>

          {/* Notes Field */}
          <TextArea
            label="Notater"
            placeholder="Prosjektspesifikke notater (valgfritt)"
            fullWidth
            rows={3}
            value={formData.notes}
            onChange={(e) => updateFormData({ notes: e.target.value })}
            helperText="Notater spesifikt for dette prosjektet (f.eks. nøkkelkoder, allergier, spesielle instruksjoner)"
          />
          </div>

        </>
      )}

      {/* General Error */}
      {errors.general && <FormError message={errors.general} />}

      {/* Navigation */}
      <div className="flex justify-between pt-4">
        <PrimaryButton
          variant="secondary"
          onClick={onPrevious}
          size="lg"
          className="btn-wizard-lg"
        >
          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
          </svg>
          Tilbake
        </PrimaryButton>

        <PrimaryButton
          variant="secondary"
          onClick={handleNext}
          disabled={(!useExistingCustomer && (!formData.customerName.trim() || !formData.streetAddress.trim() || !formData.postalCode.trim() || !formData.city.trim())) || (useExistingCustomer && !selectedCustomerId)}
          size="lg"
          className="btn-wizard-lg"
        >
          Neste
          <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
        </PrimaryButton>
      </div>
    </div>
  );
};

export default Step2CustomerInfo;
