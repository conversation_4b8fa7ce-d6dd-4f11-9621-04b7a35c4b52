import React, { useEffect, useRef } from 'react';
import { TextInput, SelectInput, PhoneInput, PrimaryButton, FormError, AddressMapPreview, AddressAutocomplete, PostalCodeInput, CompanyLookup, CompanyLookupRef, TextMuted, LockedInput, ToggleSwitch } from '../../../components/ui';
import type { AddressSuggestion } from '../../../components/ui';
import { CompanyInfo } from '../../../services/companyLookup';

// Local interface definition to avoid import issues
interface WizardFormData {
  projectName: string;
  description: string;
  customerName: string;
  customerType: 'privat' | 'bedrift';
  managingDirector: string;
  phone: string;
  email: string;
  address: string;
  streetAddress: string;
  postalCode: string;
  city: string;
  entrance: string;
  orgNumber: string;
  notes: string;
  jobDescription: string;
  accessNotes: string;
  equipmentNeeds: string;
  unresolvedQuestions: string;
  personalNotes: string;
}

interface Step2CustomerInfoProps {
  formData: WizardFormData;
  updateFormData: (updates: Partial<WizardFormData>) => void;
  errors: { [key: string]: string };
  useExistingCustomer: boolean;
  setUseExistingCustomer: (value: boolean) => void;
  selectedCustomerId: string;
  setSelectedCustomerId: (value: string) => void;
  existingCustomers: any[] | undefined;
  onNext: () => void;
  onPrevious: () => void;
  isLoading: boolean;
  setIsLoading: (value: boolean) => void;
  setErrors: (errors: { [key: string]: string }) => void;
  createProject: any;
  createCustomer: any;
  user: any;
  clearSavedData: () => void;
  setShowSuccess: (value: boolean) => void;
  navigate: (path: string) => void;
  setCreatedProjectId: (projectId: string | null) => void;
  // Brønnøysundregisteret data tracking
  brregData: any;
  setBrregData: (data: any) => void;
  brregFetchedAt: number | null;
  setBrregFetchedAt: (timestamp: number | null) => void;
  useCustomAddress: boolean;
  setUseCustomAddress: (value: boolean) => void;
  lockedFields: {
    orgNumber: boolean;
    managingDirector: boolean;
    address: boolean;
  };
  setLockedFields: (fields: any) => void;
  companySelected: boolean;
  setCompanySelected: (value: boolean) => void;
}

export const Step2CustomerInfo: React.FC<Step2CustomerInfoProps> = ({
  formData,
  updateFormData,
  errors,
  useExistingCustomer,
  setUseExistingCustomer,
  selectedCustomerId,
  setSelectedCustomerId,
  existingCustomers,
  onNext,
  onPrevious,
  isLoading,
  setIsLoading,
  setErrors,
  createProject,
  createCustomer,
  user,
  clearSavedData,
  setShowSuccess,
  navigate,
  setCreatedProjectId,
  // Brønnøysundregisteret data tracking
  brregData,
  setBrregData,
  brregFetchedAt,
  setBrregFetchedAt,
  useCustomAddress,
  setUseCustomAddress,
  lockedFields,
  setLockedFields,
  companySelected,
  setCompanySelected
}) => {
  const companyLookupRef = useRef<CompanyLookupRef>(null);
  const previousCustomerTypeRef = useRef<'privat' | 'bedrift'>('privat');

  // Filter existing customers based on selected customer type
  const filteredExistingCustomers = existingCustomers?.filter(customer => {
    // Handle both old 'firma' and new 'bedrift' values for backward compatibility
    const customerType = customer.type === 'firma' ? 'bedrift' : customer.type;
    return customerType === formData.customerType;
  }) || [];

  // Reset form when customer type changes to prevent data contamination
  useEffect(() => {
    // Only reset if customer type actually changed (not on initial load or form updates)
    if (previousCustomerTypeRef.current !== formData.customerType) {
      console.log('🔄 [Wizard] Customer type changed from', previousCustomerTypeRef.current, 'to', formData.customerType, '- resetting form fields');

      // Reset all customer-related form fields except customerType and project info
      updateFormData({
        customerName: '',
        managingDirector: '',
        phone: '',
        email: '',
        address: '',
        streetAddress: '',
        postalCode: '',
        city: '',
        entrance: '',
        orgNumber: ''
      });

      // Clear validation errors
      setErrors({});

      // Reset company lookup component
      if (companyLookupRef.current) {
        companyLookupRef.current.reset();
      }

      // Reset existing customer selection
      setUseExistingCustomer(false);
      setSelectedCustomerId('');

      // Reset company selection state
      setCompanySelected(false);
      setBrregData(null);
      setBrregFetchedAt(null);
      setUseCustomAddress(false);
      setLockedFields({
        orgNumber: false,
        managingDirector: false,
        address: false
      });

      // Update the ref to track the new customer type
      previousCustomerTypeRef.current = formData.customerType;
    }
  }, [formData.customerType, updateFormData, setErrors, setUseExistingCustomer, setSelectedCustomerId]);

  // Dynamic label and placeholder based on customer type
  const getCustomerNameLabel = () => {
    return formData.customerType === 'bedrift' ? 'Bedriftsnavn' : 'Kundenavn';
  };

  const getCustomerNamePlaceholder = () => {
    return formData.customerType === 'bedrift' ? 'F.eks. Nordmann Bygg AS' : 'F.eks. Ola Nordmann';
  };

  // Validate step 2 fields
  const validateStep = (): boolean => {
    const newErrors: { [key: string]: string } = {};

    if (!useExistingCustomer) {
      if (!formData.customerName || formData.customerName.trim().length < 2) {
        newErrors.customerName = 'Kundenavn må være minst 2 tegn langt';
      }

      // Validate individual address fields instead of combined address
      if (!formData.streetAddress || formData.streetAddress.trim().length < 3) {
        newErrors.streetAddress = 'Gateadresse må være minst 3 tegn lang';
      }

      if (!formData.postalCode || formData.postalCode.trim().length !== 4) {
        newErrors.postalCode = 'Postnummer må være 4 siffer';
      }

      if (!formData.city || formData.city.trim().length < 2) {
        newErrors.city = 'Poststed må være minst 2 tegn langt';
      }

      // Validate postal code format (4 digits)
      if (formData.postalCode && formData.postalCode.trim() !== '') {
        const postalCodePattern = /^\d{4}$/;
        if (!postalCodePattern.test(formData.postalCode.trim())) {
          newErrors.postalCode = 'Postnummer må være 4 siffer';
        }
      }

      if (formData.customerType === 'bedrift' && formData.orgNumber && formData.orgNumber.trim() !== '') {
        const orgNumberPattern = /^\d{9}$/;
        if (!orgNumberPattern.test(formData.orgNumber.trim())) {
          newErrors.orgNumber = 'Organisasjonsnummer må være 9 siffer';
        }
      }

      // Enhanced validation: Make phone and email required for both customer types
      if (formData.customerType === 'bedrift') {
        // Required fields for business customers
        if (!formData.phone || formData.phone.trim() === '') {
          newErrors.phone = 'Telefonnummer til daglig leder er påkrevd';
        }
        if (!formData.email || formData.email.trim() === '') {
          newErrors.email = 'E-postadresse til daglig leder er påkrevd';
        }
      } else {
        // Required fields for private customers
        if (!formData.phone || formData.phone.trim() === '') {
          newErrors.phone = 'Telefonnummer er påkrevd';
        }
        if (!formData.email || formData.email.trim() === '') {
          newErrors.email = 'E-postadresse er påkrevd';
        }
      }

      // Email format validation (if email is provided)
      if (formData.email && formData.email.trim() !== '') {
        const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailPattern.test(formData.email.trim())) {
          newErrors.email = 'Ugyldig e-postadresse';
        }
      }
    } else {
      if (!selectedCustomerId) {
        newErrors.selectedCustomer = 'Velg en eksisterende kunde';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validateStep()) {
      onNext();
    }
  };



  return (
    <div className="space-y-6">
      {/* Customer Selection Toggle */}
      <div className="bg-jobblogg-neutral rounded-lg p-4">
        <div className="flex items-center space-x-4">
          <label className="flex items-center">
            <input
              type="radio"
              name="customerChoice"
              checked={!useExistingCustomer}
              onChange={() => setUseExistingCustomer(false)}
              className="w-4 h-4 text-jobblogg-primary border-jobblogg-border focus:ring-jobblogg-primary/30"
            />
            <span className="ml-2 text-sm font-medium text-jobblogg-text-strong">Ny kunde</span>
          </label>
          <label className="flex items-center">
            <input
              type="radio"
              name="customerChoice"
              checked={useExistingCustomer}
              onChange={() => setUseExistingCustomer(true)}
              className="w-4 h-4 text-jobblogg-primary border-jobblogg-border focus:ring-jobblogg-primary/30"
            />
            <span className="ml-2 text-sm font-medium text-jobblogg-text-strong">Eksisterende kunde</span>
          </label>
        </div>
      </div>

      {/* Existing Customer Selection */}
      {useExistingCustomer && (
        <div className="space-y-2">
          {filteredExistingCustomers.length > 0 ? (
            <SelectInput
              label="Velg kunde"
              placeholder={`Velg en eksisterende ${formData.customerType === 'bedrift' ? 'bedrift' : 'privatkunde'}`}
              required
              fullWidth
              value={selectedCustomerId}
              onChange={(e) => setSelectedCustomerId(e.target.value)}
              error={errors.selectedCustomer}
              helperText={`Velg fra dine eksisterende ${formData.customerType === 'bedrift' ? 'bedrifter' : 'privatkunder'}`}
              options={filteredExistingCustomers.map((customer) => {
                // Use structured address fields with fallback to legacy address
                let displayAddress = 'Ingen adresse';
                if (customer.streetAddress && customer.postalCode && customer.city) {
                  displayAddress = `${customer.streetAddress}, ${customer.postalCode} ${customer.city}`;
                } else if (customer.address) {
                  displayAddress = customer.address;
                }

                return {
                  value: customer._id,
                  label: `${customer.name} - ${displayAddress}`
                };
              })}
            />
          ) : (
            <div className="p-4 bg-jobblogg-card-bg border border-jobblogg-border rounded-lg">
              <div className="text-center">
                <TextMuted className="text-sm">
                  Ingen {formData.customerType === 'bedrift' ? 'bedrifter' : 'privatkunder'} funnet.
                </TextMuted>
                <TextMuted className="text-xs mt-1">
                  Opprett en ny kunde nedenfor.
                </TextMuted>
              </div>
            </div>
          )}
        </div>
      )}

      {/* New Customer Form */}
      {!useExistingCustomer && (
        <>
          {/* Customer Type */}
          <SelectInput
            label="Kundetype"
            placeholder="Velg kundetype"
            required
            fullWidth
            value={formData.customerType}
            onChange={(e) => updateFormData({ customerType: e.target.value as 'privat' | 'bedrift' })}
            options={[
              { value: 'privat', label: 'Privat' },
              { value: 'bedrift', label: 'Bedrift' }
            ]}
          />

          {/* Customer Name with Company Lookup for Bedrift */}
          {formData.customerType === 'bedrift' ? (
            <CompanyLookup
              ref={companyLookupRef}
              companyName={formData.customerName}
              onCompanyNameChange={(name) => updateFormData({ customerName: name })}
              onCompanySelect={(company: CompanyInfo) => {
                // Store Brønnøysundregisteret data
                const brregTimestamp = Date.now();
                setBrregData(company);
                setBrregFetchedAt(brregTimestamp);

                // Auto-fill form with company information
                // Use visiting address if available, otherwise business address
                const address = company.visitingAddress || company.businessAddress;
                updateFormData({
                  customerName: company.name,
                  orgNumber: company.organizationNumber,
                  managingDirector: company.managingDirector?.fullName || '',
                  streetAddress: address?.street || '',
                  postalCode: address?.postalCode || '',
                  city: address?.city || ''
                });

                // Lock fields that were populated from Brønnøysundregisteret
                setLockedFields({
                  orgNumber: !!company.organizationNumber,
                  managingDirector: !!company.managingDirector?.fullName,
                  address: !!(company.visitingAddress || company.businessAddress)
                });

                // Reset address override when new company is selected
                setUseCustomAddress(false);

                // Mark that a company has been selected
                setCompanySelected(true);
              }}
              error={errors.customerName}
            />
          ) : (
            <TextInput
              label={getCustomerNameLabel()}
              placeholder={getCustomerNamePlaceholder()}
              required
              fullWidth
              value={formData.customerName}
              onChange={(e) => updateFormData({ customerName: e.target.value })}
              error={errors.customerName}
            />
          )}

          {/* Organization Number (for bedrift, directly under company name) */}
          {formData.customerType === 'bedrift' && (
            lockedFields.orgNumber ? (
              <LockedInput
                label="Organisasjonsnummer"
                value={formData.orgNumber}
                fullWidth
              />
            ) : (
              <TextInput
                label="Organisasjonsnummer"
                placeholder={companySelected ? "F.eks. *********" : "Velg bedrift først"}
                fullWidth
                value={formData.orgNumber}
                onChange={(e) => updateFormData({ orgNumber: e.target.value })}
                error={errors.orgNumber}
                helperText={companySelected ? "9-sifret organisasjonsnummer" : "Organisasjonsnummer fylles automatisk når du velger bedrift"}
                disabled={!companySelected}
              />
            )
          )}

          {/* Managing Director (for bedrift) */}
          {formData.customerType === 'bedrift' && (
            lockedFields.managingDirector ? (
              <LockedInput
                label="Daglig leder"
                value={formData.managingDirector}
                fullWidth
              />
            ) : (
              <TextInput
                label="Daglig leder"
                placeholder={companySelected ? "F.eks. Ola Nordmann" : "Velg bedrift først"}
                fullWidth
                value={formData.managingDirector}
                onChange={(e) => updateFormData({ managingDirector: e.target.value })}
                helperText={companySelected ? "Navn på daglig leder" : "Daglig leder fylles automatisk når du velger bedrift"}
                disabled={!companySelected}
              />
            )
          )}

          {/* Address Override Toggle and Smart Address Fields */}
          <div className="space-y-4">
            {/* Address Override Toggle (only show if we have Brreg data) */}
            {lockedFields.address && brregData && (
              <ToggleSwitch
                label="Bruk annen prosjektadresse"
                checked={useCustomAddress}
                onChange={setUseCustomAddress}
                helperText="Aktiver for å bruke en annen adresse enn bedriftens registrerte adresse"
              />
            )}
            {/* Street Address - Locked or Editable */}
            {lockedFields.address && !useCustomAddress ? (
              <LockedInput
                label="Gateadresse (Bedriftsadresse)"
                value={formData.streetAddress}
                fullWidth
              />
            ) : (
              <AddressAutocomplete
                label={lockedFields.address && useCustomAddress ? "Gateadresse (Tilpasset)" : "Gateadresse"}
                placeholder={companySelected || formData.customerType === 'privat' ? "F.eks. Storgata 15" : "Velg bedrift først"}
                required
                fullWidth
                value={formData.streetAddress}
                onChange={(value) => updateFormData({ streetAddress: value })}
                onAddressSelect={(suggestion: AddressSuggestion) => {
                  // Auto-fill postal code and city when address is selected
                  updateFormData({
                    streetAddress: suggestion.address,
                    postalCode: suggestion.postalCode,
                    city: suggestion.city
                  });
                }}
                error={errors.streetAddress}
                helperText={companySelected || formData.customerType === 'privat' ? "Start å skrive for å få forslag til adresser" : "Adresse fylles automatisk når du velger bedrift"}
                disabled={formData.customerType === 'bedrift' && !companySelected}
              />
            )}

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Postal Code - Locked or Editable */}
              {lockedFields.address && !useCustomAddress ? (
                <LockedInput
                  label="Postnummer (Bedriftsadresse)"
                  value={formData.postalCode}
                  fullWidth
                />
              ) : (
                <PostalCodeInput
                  label={lockedFields.address && useCustomAddress ? "Postnummer (Tilpasset)" : "Postnummer"}
                  required
                  fullWidth
                  value={formData.postalCode}
                  onChange={(value) => updateFormData({ postalCode: value })}
                  onCityChange={(city) => updateFormData({ city })}
                  error={errors.postalCode}
                  disabled={formData.customerType === 'bedrift' && !companySelected}
                />
              )}

              {/* City - Locked or Editable */}
              {lockedFields.address && !useCustomAddress ? (
                <LockedInput
                  label="Poststed (Bedriftsadresse)"
                  value={formData.city}
                  fullWidth
                />
              ) : (
                <TextInput
                  label={lockedFields.address && useCustomAddress ? "Poststed (Tilpasset)" : "Poststed"}
                  placeholder={companySelected || formData.customerType === 'privat' ? "F.eks. Oslo" : "Velg bedrift først"}
                  required
                  fullWidth
                  value={formData.city}
                  onChange={(e) => updateFormData({ city: e.target.value })}
                  error={errors.city}
                  helperText={companySelected || formData.customerType === 'privat' ? "Fylles automatisk ut fra postnummer" : "Poststed fylles automatisk når du velger bedrift"}
                  disabled={formData.customerType === 'bedrift' && !companySelected}
                />
              )}
            </div>

            <TextInput
              label="Oppgang/Inngang/Etasje"
              placeholder="F.eks. Oppgang A, 2. etasje (valgfritt)"
              fullWidth
              value={formData.entrance}
              onChange={(e) => updateFormData({ entrance: e.target.value })}
              helperText="Tilleggsinformasjon for å finne frem"
            />
          </div>

          {/* Address Map Preview */}
          <AddressMapPreview
            streetAddress={formData.streetAddress}
            postalCode={formData.postalCode}
            city={formData.city}
            className="mt-4"
            width={400}
            height={200}
            zoom={15}
          />

          {/* Phone and Email - Now Required */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <PhoneInput
              label={formData.customerType === 'bedrift' ? "Telefon (Daglig leder)" : "Telefon"}
              required
              fullWidth
              value={formData.phone}
              onChange={(value) => updateFormData({ phone: value })}
              error={errors.phone}
              helperText={formData.customerType === 'bedrift' ? "Telefonnummer til daglig leder (påkrevd)" : "Norsk mobilnummer (påkrevd)"}
            />
            <TextInput
              label={formData.customerType === 'bedrift' ? "E-post (Daglig leder)" : "E-post"}
              type="email"
              placeholder="F.eks. <EMAIL>"
              required
              fullWidth
              value={formData.email}
              onChange={(e) => updateFormData({ email: e.target.value })}
              error={errors.email}
              helperText={formData.customerType === 'bedrift' ? "E-postadresse til daglig leder (påkrevd)" : "E-postadresse (påkrevd)"}
            />
          </div>


        </>
      )}

      {/* General Error */}
      {errors.general && <FormError message={errors.general} />}

      {/* Navigation */}
      <div className="flex justify-between pt-4">
        <PrimaryButton
          variant="secondary"
          onClick={onPrevious}
          size="lg"
          className="btn-wizard-lg"
        >
          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
          </svg>
          Tilbake
        </PrimaryButton>

        <PrimaryButton
          variant="secondary"
          onClick={handleNext}
          disabled={(!useExistingCustomer && (!formData.customerName.trim() || !formData.streetAddress.trim() || !formData.postalCode.trim() || !formData.city.trim())) || (useExistingCustomer && !selectedCustomerId)}
          size="lg"
          className="btn-wizard-lg"
        >
          Neste
          <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
        </PrimaryButton>
      </div>
    </div>
  );
};

export default Step2CustomerInfo;
