import React, { useState, useRef, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, PrimaryButton, FormError } from '../../../components/ui';

// Local interface definition to avoid import issues
interface WizardFormData {
  projectName: string;
  description: string;
  customerName: string;
  customerType: 'privat' | 'bedrift';
  contactPerson: string;
  phone: string;
  email: string;
  address: string;
  streetAddress: string;
  postalCode: string;
  city: string;
  entrance: string;
  orgNumber: string;
  notes: string;
  jobDescription: string;
  accessNotes: string;
  equipmentNeeds: string;
  unresolvedQuestions: string;
  personalNotes: string;
  selectedImages: string[];
  imageFiles: File[];
}

interface Step3JobDescriptionProps {
  formData: WizardFormData;
  updateFormData: (updates: Partial<WizardFormData>) => void;
  errors: { [key: string]: string };
  onPrevious: () => void;
  isLoading: boolean;
  setIsLoading: (value: boolean) => void;
  setErrors: (errors: { [key: string]: string }) => void;
  updateProjectJobData: any;
  generateUploadUrl: any;
  storeJobImage: any;
  createProject: any;
  createCustomer: any;
  user: any;
  clearSavedData: () => void;
  setShowSuccess: (value: boolean) => void;
  navigate: (path: string) => void;
  createdProjectId: string | null;
  useExistingCustomer: boolean;
  selectedCustomerId: string;
  // Brønnøysundregisteret data tracking
  brregData: any;
  brregFetchedAt: number | null;
  useCustomAddress: boolean;
}

export const Step3JobDescription: React.FC<Step3JobDescriptionProps> = ({
  formData,
  updateFormData,
  errors,
  onPrevious,
  isLoading,
  setIsLoading,
  setErrors,
  updateProjectJobData,
  generateUploadUrl,
  storeJobImage,
  createProject,
  createCustomer,
  user,
  clearSavedData,
  setShowSuccess,
  navigate,
  createdProjectId,
  useExistingCustomer,
  selectedCustomerId,
  // Brønnøysundregisteret data tracking
  brregData,
  brregFetchedAt,
  useCustomAddress
}) => {
  // Image handling refs
  const fileInputRef = useRef<HTMLInputElement>(null);
  const cameraInputRef = useRef<HTMLInputElement>(null);

  // Convert base64 strings back to File objects for display
  const [imagePreviews, setImagePreviews] = useState<string[]>([]);

  // Initialize image previews from formData on component mount
  useEffect(() => {
    if (formData.selectedImages.length > 0) {
      setImagePreviews(formData.selectedImages);
    }
  }, [formData.selectedImages]);

  // Helper function to convert File to base64
  const fileToBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = error => reject(error);
    });
  };

  // Helper function to convert base64 to File
  const base64ToFile = (base64: string, filename: string): File => {
    const arr = base64.split(',');
    const mime = arr[0].match(/:(.*?);/)?.[1] || 'image/jpeg';
    const bstr = atob(arr[1]);
    let n = bstr.length;
    const u8arr = new Uint8Array(n);
    while (n--) {
      u8arr[n] = bstr.charCodeAt(n);
    }
    return new File([u8arr], filename, { type: mime });
  };

  // Handle image selection from gallery
  const handleImageSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    if (files.length === 0) return;

    try {
      // Convert files to base64 for persistence
      const base64Images = await Promise.all(files.map(file => fileToBase64(file)));

      // Update formData with new images
      updateFormData({
        selectedImages: [...formData.selectedImages, ...base64Images],
        imageFiles: [...formData.imageFiles, ...files]
      });

      // Update previews
      setImagePreviews(prev => [...prev, ...base64Images]);

      // Reset input
      if (event.target) {
        event.target.value = '';
      }
    } catch (error) {
      console.error('Error processing images:', error);
    }
  };

  // Handle camera capture
  const handleCameraCapture = (event: React.ChangeEvent<HTMLInputElement>) => {
    handleImageSelect(event);
  };

  // Remove image
  const removeImage = (index: number) => {
    // Update formData by removing the image at the specified index
    updateFormData({
      selectedImages: formData.selectedImages.filter((_, i) => i !== index),
      imageFiles: formData.imageFiles.filter((_, i) => i !== index)
    });

    // Update previews
    setImagePreviews(prev => prev.filter((_, i) => i !== index));
  };

  // Cleanup is not needed for base64 images as they don't create object URLs

  // Create complete project with job data
  const handleCreateCompleteProject = async () => {
    console.log('🚀 Starting project creation process...');
    setIsLoading(true);
    setErrors({});

    try {
      if (!user?.id) {
        console.error('❌ User not authenticated');
        setIsLoading(false);
        return;
      }

      console.log('✅ User authenticated:', user.id);

      let projectId = createdProjectId;

      // If no project was created in step 2, create it now
      if (!projectId) {
        let customerId;

        if (useExistingCustomer) {
          customerId = selectedCustomerId;
        } else {
          // Create new customer
          customerId = await createCustomer({
            name: formData.customerName.trim(),
            type: formData.customerType,
            contactPerson: formData.contactPerson.trim() || undefined,
            phone: formData.phone.trim() || undefined,
            email: formData.email.trim() || undefined,
            // Use new structured address fields
            streetAddress: formData.streetAddress.trim(),
            postalCode: formData.postalCode.trim(),
            city: formData.city.trim(),
            entrance: formData.entrance.trim() || undefined,
            orgNumber: formData.orgNumber.trim() || undefined,
            notes: formData.notes.trim() || undefined,
            // Brønnøysundregisteret data tracking
            brregFetchedAt: brregFetchedAt || undefined,
            brregData: brregData || undefined,
            useCustomAddress: useCustomAddress || undefined,
            userId: user.id
          });
        }

        // Create project with customer reference
        console.log('📝 Creating project with data:', {
          name: formData.projectName.trim(),
          description: formData.description.trim(),
          userId: user.id,
          customerId: customerId
        });

        projectId = await createProject({
          name: formData.projectName.trim(),
          description: formData.description.trim(),
          userId: user.id,
          customerId: customerId as any
        });

        console.log('✅ Project created with ID:', projectId);
      }

      // Add job data to the project if any job information is provided
      const hasJobData = formData.jobDescription || formData.accessNotes ||
                        formData.equipmentNeeds || formData.unresolvedQuestions ||
                        formData.personalNotes;

      if (hasJobData && projectId) {
        // Upload images to Convex storage first
        const uploadedPhotos = [];

        // Convert base64 images back to File objects for upload
        const filesToUpload = formData.imageFiles.length > 0
          ? formData.imageFiles
          : formData.selectedImages.map((base64, index) =>
              base64ToFile(base64, `image-${index}.jpg`)
            );

        for (const file of filesToUpload) {
          try {
            // Get upload URL from Convex
            const uploadUrl = await generateUploadUrl();

            // Upload the file to Convex storage
            const result = await fetch(uploadUrl, {
              method: 'POST',
              headers: { 'Content-Type': file.type },
              body: file,
            });

            if (!result.ok) {
              throw new Error(`Failed to upload image: ${file.name}`);
            }

            const { storageId } = await result.json();

            // Get the actual URL from Convex storage
            const { url } = await storeJobImage({
              projectId: projectId as any,
              userId: user.id,
              storageId
            });

            uploadedPhotos.push({
              url,
              note: '', // Empty note for now
              capturedAt: Date.now()
            });
          } catch (error) {
            console.error('Error uploading image:', file.name, error);
            // Continue with other images even if one fails
          }
        }

        await updateProjectJobData({
          projectId: projectId as any,
          userId: user.id,
          jobData: {
            jobDescription: formData.jobDescription,
            photos: uploadedPhotos,
            accessNotes: formData.accessNotes,
            equipmentNeeds: formData.equipmentNeeds,
            unresolvedQuestions: formData.unresolvedQuestions,
            personalNotes: formData.personalNotes
          }
        });
      }

      // Clear saved data and show success
      console.log('🎉 Project creation completed successfully!');
      clearSavedData();
      setShowSuccess(true);
      setTimeout(() => {
        setShowSuccess(false);
        navigate('/');
      }, 2000);

    } catch (error) {
      console.error('❌ Error creating complete project:', error);
      console.error('Error details:', {
        message: error.message,
        stack: error.stack,
        formData: formData,
        userId: user?.id
      });
      setErrors({ general: 'Det oppstod en feil ved opprettelse av prosjektet. Prøv igjen.' });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Privacy Warning for Contractor Notes */}
      <div className="bg-jobblogg-accent-soft border border-jobblogg-accent/20 rounded-lg p-4">
        <div className="flex items-start gap-3">
          <div className="w-5 h-5 text-jobblogg-accent mt-0.5 flex-shrink-0">
            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div>
            <h4 className="font-semibold text-jobblogg-text-strong mb-1">🔒 Prosjektnotater</h4>
            <p className="text-sm text-jobblogg-text-medium">
              Disse notatene er alltid kun synlige for deg som leverandør og deles aldri med kunder.
              Bruk dette for interne arbeidsnotater, tilgangsinformasjon og andre detaljer som skal holdes private.
            </p>
          </div>
        </div>
      </div>

      {/* Job Description */}
      <TextArea
        label="Detaljerte arbeidsinstruksjoner"
        placeholder="Beskriv steg-for-steg hva som skal gjøres, hvilke materialer som trengs, etc..."
        fullWidth
        rows={4}
        value={formData.jobDescription}
        onChange={(e) => updateFormData({ jobDescription: e.target.value })}
        helperText="💡 Tips: Vær så spesifikk som mulig - dette er dine arbeidsinstruksjoner"
      />

      {/* Access Notes */}
      <TextArea
        label="Tilgangsnotater"
        placeholder="Informasjon om tilgang til arbeidsstedet, nøkler, koder, etc..."
        fullWidth
        rows={3}
        value={formData.accessNotes}
        onChange={(e) => updateFormData({ accessNotes: e.target.value })}
        helperText="F.eks. 'Nøkkel under blomsterpotten', 'Ring på dørklokka', 'Kode til port: 1234'"
      />

      {/* Equipment Needs */}
      <TextArea
        label="Utstyrsbehov"
        placeholder="Liste over verktøy, materialer eller utstyr som trengs..."
        fullWidth
        rows={3}
        value={formData.equipmentNeeds}
        onChange={(e) => updateFormData({ equipmentNeeds: e.target.value })}
        helperText="F.eks. 'Borhammer', 'Stige 3m', 'Maling - hvit'"
      />

      {/* Unresolved Questions */}
      <TextArea
        label="Uavklarte spørsmål"
        placeholder="Spørsmål som må avklares med kunden før eller under jobben..."
        fullWidth
        rows={3}
        value={formData.unresolvedQuestions}
        onChange={(e) => updateFormData({ unresolvedQuestions: e.target.value })}
        helperText="F.eks. 'Hvilken farge på flisene?', 'Skal vi male taket også?'"
      />

      {/* Personal Notes */}
      <TextArea
        label="Personlige notater"
        placeholder="Dine egne notater og påminnelser for prosjektet..."
        fullWidth
        rows={3}
        value={formData.personalNotes}
        onChange={(e) => updateFormData({ personalNotes: e.target.value })}
        helperText="Private notater som kun du ser"
      />

      {/* Image Capture Section */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-jobblogg-text-strong">Bilder fra befaring</h3>
          <div className="flex gap-2">
            {/* Camera Button */}
            <button
              type="button"
              onClick={() => cameraInputRef.current?.click()}
              className="inline-flex items-center px-3 py-2 text-sm font-medium text-jobblogg-primary bg-jobblogg-primary-soft border border-jobblogg-primary/20 rounded-lg hover:bg-jobblogg-primary/10 focus:outline-none focus:ring-2 focus:ring-jobblogg-primary/20 transition-colors duration-200"
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
              Ta bilde
            </button>

            {/* Gallery Button */}
            <button
              type="button"
              onClick={() => fileInputRef.current?.click()}
              className="inline-flex items-center px-3 py-2 text-sm font-medium text-jobblogg-accent bg-jobblogg-accent-soft border border-jobblogg-accent/20 rounded-lg hover:bg-jobblogg-accent/10 focus:outline-none focus:ring-2 focus:ring-jobblogg-accent/20 transition-colors duration-200"
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
              Velg bilder
            </button>
          </div>
        </div>

        {/* Hidden File Inputs */}
        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          multiple
          onChange={handleImageSelect}
          className="hidden"
        />
        <input
          ref={cameraInputRef}
          type="file"
          accept="image/*"
          capture="environment"
          onChange={handleCameraCapture}
          className="hidden"
        />

        {/* Image Previews */}
        {imagePreviews.length > 0 && (
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {imagePreviews.map((preview, index) => (
              <div key={index} className="relative group">
                <img
                  src={preview}
                  alt={`Forhåndsvisning ${index + 1}`}
                  className="w-full h-24 object-cover rounded-lg border border-jobblogg-border"
                />
                <button
                  type="button"
                  onClick={() => removeImage(index)}
                  className="absolute -top-2 -right-2 w-6 h-6 bg-jobblogg-error text-white rounded-full flex items-center justify-center text-xs hover:bg-jobblogg-error-dark transition-colors duration-200 opacity-0 group-hover:opacity-100"
                  aria-label="Fjern bilde"
                >
                  ×
                </button>
              </div>
            ))}
          </div>
        )}

        {imagePreviews.length === 0 && (
          <div className="text-center py-8 border-2 border-dashed border-jobblogg-border rounded-lg bg-jobblogg-background-soft">
            <svg className="w-12 h-12 mx-auto text-jobblogg-text-muted mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
            <p className="text-jobblogg-text-muted text-sm">
              Ingen bilder valgt. Bruk knappene ovenfor for å legge til bilder.
            </p>
          </div>
        )}
      </div>

      {/* General Error */}
      {errors.general && <FormError message={errors.general} />}

      {/* Navigation */}
      <div className="flex justify-between pt-4">
        <PrimaryButton
          variant="secondary"
          onClick={onPrevious}
          size="lg"
          className="btn-wizard-lg"
        >
          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
          </svg>
          Tilbake
        </PrimaryButton>

        <PrimaryButton
          onClick={handleCreateCompleteProject}
          disabled={isLoading}
          loading={isLoading}
          size="lg"
          className="btn-wizard-lg"
        >
          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
          Opprett Prosjekt
        </PrimaryButton>
      </div>

      {/* Completion Summary */}
      <div className="bg-jobblogg-primary-soft rounded-lg p-4 mt-6">
        <h4 className="font-semibold text-jobblogg-text-strong mb-2">📋 Prosjektsammendrag</h4>
        <div className="space-y-1 text-sm text-jobblogg-text-muted">
          <p><strong>Prosjekt:</strong> {formData.projectName}</p>
          <p><strong>Kunde:</strong> {formData.customerName || 'Eksisterende kunde'}</p>
          <p><strong>Type:</strong> {formData.customerType === 'firma' ? 'Firma' : 'Privat'}</p>
          {formData.description && (
            <p><strong>Sammendrag:</strong> <span className="whitespace-pre-wrap break-words">{formData.description}</span></p>
          )}
        </div>
      </div>
    </div>
  );
};

export default Step3JobDescription;
