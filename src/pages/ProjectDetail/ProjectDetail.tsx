import React, { useState, useEffect, useCallback } from 'react';
import { Link, useParams, useNavigate } from 'react-router-dom';
import { useQuery, useMutation } from 'convex/react';
import { useUser } from '@clerk/clerk-react';
import { api } from '../../../convex/_generated/api';
import { PageLayout, Heading2, Heading3, BodyText, TextMuted, PrimaryButton, ArchiveActions, ArchiveStatusBadge, TextArea } from '../../components/ui';
import { ShareProjectModal } from '../../components/ShareProjectModal';
import { CustomerInformationCard } from '../../components/CustomerInformationCard';

// Job data interface
interface JobData {
  jobDescription: string;
  photos: Array<{
    url: string;
    note?: string;
    capturedAt?: number;
  }>;
  accessNotes: string;
  equipmentNeeds: string;
  unresolvedQuestions: string;
  personalNotes: string;
}

const ProjectDetail: React.FC = () => {
  const { projectId } = useParams<{ projectId: string }>();
  const { user } = useUser();
  const navigate = useNavigate();

  // Job information state
  const [isJobSectionExpanded, setIsJobSectionExpanded] = useState(true);
  const [jobData, setJobData] = useState<JobData>({
    jobDescription: '',
    photos: [],
    accessNotes: '',
    equipmentNeeds: '',
    unresolvedQuestions: '',
    personalNotes: ''
  });
  const [isSaving, setIsSaving] = useState(false);
  const [saveError, setSaveError] = useState<string | null>(null);
  const [uploadingImages, setUploadingImages] = useState<Set<string>>(new Set());

  // Share modal state
  const [showShareModal, setShowShareModal] = useState(false);

  // Mutations
  const updateJobData = useMutation(api.projects.updateProjectJobData);
  const storeJobImage = useMutation(api.projects.storeJobImage);
  const generateUploadUrl = useMutation(api.logEntries.generateUploadUrl);

  // Format date in short format
  const formatShortDate = (timestamp: number): string => {
    try {
      const date = new Date(timestamp);
      if (isNaN(date.getTime())) return 'Ugyldig dato';

      const day = date.getDate().toString().padStart(2, '0');
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const year = date.getFullYear();

      return `${day}.${month}.${year}`;
    } catch (error) {
      return 'Ugyldig dato';
    }
  };

  // Format Norwegian date and time
  const formatNorwegianDateTime = (timestamp: number): string => {
    try {
      const date = new Date(timestamp);
      if (isNaN(date.getTime())) return 'Ugyldig dato';

      const day = date.getDate().toString().padStart(2, '0');
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const year = date.getFullYear();
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');

      return `${day}.${month}.${year} ${hours}:${minutes}`;
    } catch (error) {
      return 'Ugyldig dato';
    }
  };







  // Fetch project details
  const project = useQuery(
    api.projects.getById,
    projectId ? { projectId: projectId as any } : "skip"
  );

  // Fetch log entries for this project
  const logEntries = useQuery(
    api.logEntries.getByProject,
    projectId && user?.id && project ? {
      projectId: projectId as any,
      userId: user.id
    } : "skip"
  );

  // Initialize job data when project loads
  useEffect(() => {
    if (project?.jobData) {
      setJobData(project.jobData);
      setIsJobSectionExpanded(true);
    }
  }, [project]);

  // Debounced autosave function
  const debouncedSave = useCallback(
    (() => {
      let timeoutId: number;
      return (data: JobData) => {
        clearTimeout(timeoutId);
        timeoutId = setTimeout(async () => {
          if (!projectId || !user?.id) return;

          try {
            setIsSaving(true);
            setSaveError(null);
            await updateJobData({
              projectId: projectId as any,
              userId: user.id,
              jobData: data
            });
          } catch (error) {
            console.error('Failed to save job data:', error);
            setSaveError('Kunne ikke lagre endringene. Prøv igjen.');
          } finally {
            setIsSaving(false);
          }
        }, 1000);
      };
    })(),
    [projectId, user?.id, updateJobData]
  );

  // Handle job data changes with autosave
  const handleJobDataChange = (field: keyof JobData, value: string | Array<any>) => {
    const newData = { ...jobData, [field]: value };
    setJobData(newData);
    debouncedSave(newData);
  };

  // Handle image upload for job photos
  const handleImageUpload = async (files: FileList) => {
    if (!files.length || !projectId || !user?.id) return;

    const newUploadingImages = new Set(uploadingImages);

    try {
      for (const file of Array.from(files)) {
        if (!file.type.startsWith('image/')) continue;

        const uploadId = `${Date.now()}-${Math.random()}`;
        newUploadingImages.add(uploadId);
        setUploadingImages(new Set(newUploadingImages));

        // Generate upload URL
        const uploadUrl = await generateUploadUrl();

        // Upload file
        const result = await fetch(uploadUrl, {
          method: 'POST',
          headers: { 'Content-Type': file.type },
          body: file,
        });

        if (!result.ok) {
          throw new Error(`Upload failed: ${result.statusText}`);
        }

        const { storageId } = await result.json();

        // Store image metadata
        await storeJobImage({
          projectId: projectId as any,
          userId: user.id,
          storageId
        });

        // Add to local state
        const imageUrl = URL.createObjectURL(file);
        const newPhotos = [...jobData.photos, {
          url: imageUrl,
          note: '',
          capturedAt: Date.now()
        }];

        handleJobDataChange('photos', newPhotos);

        newUploadingImages.delete(uploadId);
        setUploadingImages(new Set(newUploadingImages));
      }
    } catch (error) {
      console.error('Error uploading images:', error);
      setSaveError('Kunne ikke laste opp bilder. Prøv igjen.');
      setUploadingImages(new Set());
    }
  };

  // Handle photo deletion
  const handlePhotoDelete = (index: number) => {
    const newPhotos = jobData.photos.filter((_, i) => i !== index);
    handleJobDataChange('photos', newPhotos);
  };

  // Handle photo note update
  const handlePhotoNoteUpdate = (index: number, note: string) => {
    const newPhotos = jobData.photos.map((photo, i) =>
      i === index ? { ...photo, note } : photo
    );
    handleJobDataChange('photos', newPhotos);
  };



















  // Modern loading state with skeletons
  if (project === undefined || logEntries === undefined) {
    return (
      <div className="min-h-screen bg-white">
        <div className="container mx-auto px-4 py-8 max-w-6xl">
          {/* Header Skeleton */}
          <div className="flex items-center gap-4 mb-8">
            <div className="skeleton h-10 w-10 rounded-full"></div>
            <div className="skeleton h-8 w-64"></div>
            <div className="ml-auto skeleton h-10 w-32 rounded-lg"></div>
          </div>

          {/* Project Info Skeleton */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12">
            <div className="lg:col-span-2">
              <div className="bg-base-100 rounded-xl p-8 shadow-lg">
                <div className="skeleton h-8 w-48 mb-4"></div>
                <div className="skeleton h-20 w-full mb-6"></div>
                <div className="flex gap-4">
                  <div className="skeleton h-12 w-32 rounded-lg"></div>
                  <div className="skeleton h-12 w-32 rounded-lg"></div>
                </div>
              </div>
            </div>
            <div>
              <div className="bg-base-100 rounded-xl p-6 shadow-lg">
                <div className="skeleton h-6 w-24 mb-4"></div>
                <div className="space-y-3">
                  <div className="skeleton h-4 w-full"></div>
                  <div className="skeleton h-4 w-3/4"></div>
                  <div className="skeleton h-4 w-1/2"></div>
                </div>
              </div>
            </div>
          </div>

          {/* Gallery Skeleton */}
          <div className="bg-white rounded-xl p-8 shadow-lg border border-jobblogg-border">
            <div className="skeleton h-8 w-32 mb-6"></div>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
              {[...Array(6)].map((_, i) => (
                <div key={i} className="skeleton h-48 w-full rounded-xl"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Modern error state for unauthorized/not found
  if (!project || project.userId !== user?.id) {
    return (
      <div className="min-h-screen bg-base-200/30 animate-fade-in">
        <div className="container mx-auto px-4 py-8 max-w-4xl">
          <div className="flex items-center gap-4 mb-8">
            <Link
              to="/"
              className="btn btn-ghost btn-circle btn-modern hover:bg-jobblogg-primary/10"
              aria-label="Tilbake til oversikt"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </Link>
          </div>

          <div className="bg-jobblogg-error-soft border border-jobblogg-error/20 rounded-xl p-8 text-center">
            <div className="w-16 h-16 mx-auto mb-4 bg-jobblogg-error/20 rounded-full flex items-center justify-center">
              <svg className="w-8 h-8 text-jobblogg-error" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <Heading2 className="text-jobblogg-error mb-2">Prosjekt ikke funnet</Heading2>
            <BodyText className="text-jobblogg-error/80 mb-6">
              Dette prosjektet eksisterer ikke eller du har ikke tilgang til det.
            </BodyText>
            <PrimaryButton onClick={() => navigate('/')}>
              Tilbake til oversikt
            </PrimaryButton>
          </div>
        </div>
      </div>
    );
  }

  // Sort log entries by creation date (newest first)
  const sortedLogEntries = logEntries.sort((a, b) => b.createdAt - a.createdAt);

  // Filter for user-created entries only (excludes system activity entries like archive/restore)
  const userLogEntries = sortedLogEntries.filter(entry =>
    entry.entryType !== "system" // Include entries that are explicitly "user" or have no entryType (backward compatibility)
  );

  // Filter for entries with actual images for the image gallery
  const imageEntries = userLogEntries.filter(entry => entry.imageUrl);

  return (
    <PageLayout
      showBackButton
      backUrl="/"
      containerWidth="wide"
      headerActions={
        <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 sm:gap-3 w-full sm:w-auto">
          <ArchiveStatusBadge
            isArchived={project.isArchived}
            archivedAt={project.archivedAt}
          />
          <div className="flex items-center gap-2 px-3 py-1.5 bg-jobblogg-primary-soft text-jobblogg-primary rounded-full text-sm font-medium min-h-[44px]">
            <svg className="w-4 h-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span className="hidden sm:inline whitespace-nowrap">Prosjektdetaljer</span>
            <span className="sm:hidden">Detaljer</span>
          </div>

          <div className="flex flex-col sm:flex-row gap-2 w-full sm:w-auto">
            <PrimaryButton
              onClick={() => setShowShareModal(true)}
              variant="secondary"
              className="flex items-center justify-center gap-2 min-h-[44px] w-full sm:w-auto px-4 py-2"
            >
              <svg className="w-4 h-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
              </svg>
              <span className="text-sm sm:text-base whitespace-nowrap">Del prosjekt</span>
            </PrimaryButton>
          </div>
        </div>
      }
    >
      <div className="w-full px-4 sm:px-0 sm:max-w-4xl sm:mx-auto space-y-6 sm:space-y-8">

        {/* Project Header */}
        <div className="text-center space-y-3 sm:space-y-4 animate-slide-up">
          <div className="w-12 h-12 sm:w-16 sm:h-16 mx-auto bg-jobblogg-primary-soft rounded-full flex items-center justify-center">
            <svg className="w-6 h-6 sm:w-8 sm:h-8 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-jobblogg-text-strong leading-tight break-words px-2 sm:px-0">
            {project.name}
          </h1>
        </div>

        {/* Project Information */}
        <div className="bg-white rounded-xl border border-jobblogg-border shadow-soft p-6 sm:p-8 space-y-6">
          <Heading2>Prosjektinformasjon</Heading2>

          <div>
            <Heading3>Prosjektsammendrag</Heading3>
            <div className="bg-jobblogg-neutral rounded-lg p-4 border border-jobblogg-border">
              <BodyText className="leading-relaxed">
                {project.description || (
                  <TextMuted className="italic">
                    Ingen sammendrag tilgjengelig. Du kan legge til et sammendrag ved å redigere prosjektet.
                  </TextMuted>
                )}
              </BodyText>
            </div>
          </div>

          {/* Enhanced Customer Information */}
          {project.customer && (
            <CustomerInformationCard
              customer={project.customer as any}
              projectId={projectId!}
              onCustomerUpdate={(updatedCustomer) => {
                // Optionally handle customer update in parent component
                console.log('Customer updated:', updatedCustomer);
              }}
            />
          )}
        </div>

        {/* Job Information */}
        <div className="bg-white rounded-xl border border-jobblogg-border shadow-soft p-6 sm:p-8 space-y-6">
          <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-3 sm:gap-4">
            <Heading2 className="min-w-0 flex-shrink">Jobbinformasjon</Heading2>
            <button
              onClick={() => setIsJobSectionExpanded(!isJobSectionExpanded)}
              className="flex items-center gap-2 px-3 py-1.5 bg-jobblogg-neutral rounded-lg border border-jobblogg-border hover:bg-jobblogg-primary-soft transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-jobblogg-primary/30 min-h-[44px] w-full sm:w-auto flex-shrink-0"
              aria-expanded={isJobSectionExpanded}
            >
              <span className="text-xs sm:text-sm font-medium whitespace-nowrap">
                {isJobSectionExpanded ? 'Skjul detaljer' : 'Vis detaljer'}
              </span>
              <svg
                className={`w-4 h-4 flex-shrink-0 transition-transform duration-200 ${isJobSectionExpanded ? 'rotate-180' : ''}`}
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
              {isSaving && (
                <div className="flex items-center gap-1 text-jobblogg-text-muted">
                  <svg className="w-3 h-3 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                  <span className="text-xs">Lagrer...</span>
                </div>
              )}
            </button>
          </div>

          {saveError && (
            <div className="bg-jobblogg-error-soft border border-jobblogg-error/20 rounded-lg p-3 flex items-center gap-2">
              <svg className="w-4 h-4 text-jobblogg-error flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
              <span className="text-sm text-jobblogg-error">{saveError}</span>
            </div>
          )}

          {isJobSectionExpanded && (
            <div className="space-y-6 bg-white rounded-lg p-6 border border-jobblogg-border">
              {/* Job Description */}
              <div>
                <TextArea
                  label="Detaljerte arbeidsinstruksjoner"
                  placeholder="Beskriv steg-for-steg hva som skal gjøres, materialer, etc..."
                  value={jobData.jobDescription}
                  onChange={(e) => handleJobDataChange('jobDescription', e.target.value)}
                  rows={4}
                  fullWidth
                  className="min-h-[100px]"
                  disabled={project?.isArchived}
                />
              </div>

              {/* Access Notes */}
              <div>
                <TextArea
                  label="Tilkomst og forhold"
                  placeholder="Beskriv tilkomst til arbeidsstedet, parkeringsmuligheter, nøkler, etc..."
                  value={jobData.accessNotes}
                  onChange={(e) => handleJobDataChange('accessNotes', e.target.value)}
                  rows={3}
                  fullWidth
                  disabled={project?.isArchived}
                />
              </div>

              {/* Equipment Needs */}
              <div>
                <TextArea
                  label="Hva må medbringes?"
                  placeholder="Liste over verktøy, materialer og utstyr som trengs..."
                  value={jobData.equipmentNeeds}
                  onChange={(e) => handleJobDataChange('equipmentNeeds', e.target.value)}
                  rows={3}
                  fullWidth
                  disabled={project?.isArchived}
                />
              </div>

              {/* Unresolved Questions */}
              <div>
                <TextArea
                  label="Hva må avklares?"
                  placeholder="Spørsmål som må avklares med kunden eller andre..."
                  value={jobData.unresolvedQuestions}
                  onChange={(e) => handleJobDataChange('unresolvedQuestions', e.target.value)}
                  rows={3}
                  fullWidth
                  disabled={project?.isArchived}
                />
              </div>

              {/* Personal Notes */}
              <div>
                <TextArea
                  label="Egne notater"
                  placeholder="Dine personlige notater og observasjoner..."
                  value={jobData.personalNotes}
                  onChange={(e) => handleJobDataChange('personalNotes', e.target.value)}
                  rows={3}
                  fullWidth
                  disabled={project?.isArchived}
                />
              </div>

              {/* Photo Upload Section */}
              <div>
                <label className="block text-sm font-medium mb-3 text-jobblogg-text-strong">
                  Bilder fra befaring
                </label>

                {/* Upload Button */}
                {!project?.isArchived && (
                  <div className="mb-4">
                    <input
                      type="file"
                      id="job-photo-upload"
                      multiple
                      accept="image/*"
                      capture="environment"
                      onChange={(e) => e.target.files && handleImageUpload(e.target.files)}
                      className="hidden"
                      aria-describedby="upload-help"
                    />
                    <label
                      htmlFor="job-photo-upload"
                      className="
                        inline-flex items-center gap-2 px-4 py-3
                        bg-jobblogg-primary text-white rounded-lg
                        hover:bg-jobblogg-primary-dark transition-colors duration-200
                        focus-within:ring-2 focus-within:ring-jobblogg-primary/30
                        cursor-pointer min-h-[44px]
                      "
                      role="button"
                      tabIndex={0}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter' || e.key === ' ') {
                          e.preventDefault();
                          document.getElementById('job-photo-upload')?.click();
                        }
                      }}
                    >
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
                      </svg>
                      Ta bilde / Last opp
                    </label>
                    <p id="upload-help" className="mt-1 text-xs text-jobblogg-text-muted">
                      Støtter JPG, PNG og andre bildeformater. Flere bilder kan velges samtidig.
                    </p>
                    {uploadingImages.size > 0 && (
                      <div
                        className="mt-2 text-sm text-jobblogg-text-muted flex items-center gap-1"
                        aria-live="polite"
                        role="status"
                      >
                        <svg className="w-3 h-3 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                        </svg>
                        <span>Laster opp {uploadingImages.size} bilde{uploadingImages.size > 1 ? 'r' : ''}...</span>
                      </div>
                    )}
                  </div>
                )}

                {/* Photo Grid */}
                {jobData.photos.length > 0 && (
                  <div
                    className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4"
                    role="grid"
                    aria-label="Bilder fra befaring"
                  >
                    {jobData.photos.map((photo, index) => (
                      <div
                        key={index}
                        className="relative bg-jobblogg-neutral rounded-lg overflow-hidden border border-jobblogg-border"
                        role="gridcell"
                      >
                        <div className="aspect-square relative">
                          <img
                            src={photo.url}
                            alt={photo.note ? `Befaring bilde ${index + 1}: ${photo.note}` : `Befaring bilde ${index + 1}`}
                            className="w-full h-full object-cover"
                            loading="lazy"
                          />

                          {/* Delete Button */}
                          <button
                            onClick={() => handlePhotoDelete(index)}
                            className="
                              absolute top-2 right-2 p-1.5
                              bg-jobblogg-error text-white rounded-full
                              hover:bg-jobblogg-error-dark transition-colors duration-200
                              focus:outline-none focus:ring-2 focus:ring-jobblogg-error/30
                              min-h-[44px] min-w-[44px] flex items-center justify-center
                            "
                            aria-label={`Slett bilde ${index + 1}${photo.note ? `: ${photo.note}` : ''}`}
                            title={`Slett bilde ${index + 1}`}
                          >
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                            </svg>
                          </button>
                        </div>

                        {/* Photo Note */}
                        <div className="p-3">
                          <TextArea
                            label={`Kommentar til bilde ${index + 1}`}
                            placeholder="Legg til kommentar til bildet..."
                            value={photo.note || ''}
                            onChange={(e) => handlePhotoNoteUpdate(index, e.target.value)}
                            rows={2}
                            fullWidth
                            size="small"
                            aria-describedby={photo.capturedAt ? `photo-${index}-timestamp` : undefined}
                            disabled={project?.isArchived}
                          />
                          {photo.capturedAt && (
                            <div
                              id={`photo-${index}-timestamp`}
                              className="mt-2 text-xs text-jobblogg-text-muted"
                              aria-label={`Tatt ${formatNorwegianDateTime(photo.capturedAt)}`}
                            >
                              {formatNorwegianDateTime(photo.capturedAt)}
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                )}

                {jobData.photos.length === 0 && (
                  <div className="text-center py-8 text-jobblogg-text-muted">
                    <svg className="w-12 h-12 mx-auto mb-3 text-jobblogg-text-muted/50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                    <p className="text-sm">Ingen bilder lagt til ennå</p>
                    <p className="text-xs mt-1">Ta bilder av arbeidsstedet for dokumentasjon</p>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-3 pt-4 border-t border-jobblogg-border">
            <ArchiveActions
              projectId={projectId!}
              isArchived={project?.isArchived}
              onArchiveComplete={() => navigate('/', { replace: true })}
              onRestoreComplete={() => navigate('/', { replace: true })}
              className="min-h-[44px] w-full sm:w-auto flex-1 sm:flex-none"
            />
          </div>
        </div>

        {/* Statistics */}
        <div className="bg-white rounded-xl border border-jobblogg-border shadow-soft p-6 sm:p-8 space-y-6">
          <Heading2>Statistikk</Heading2>

          <div className="grid grid-cols-2 gap-4">
            <div className="bg-jobblogg-primary-soft rounded-lg p-3 sm:p-4 text-center">
              <div className="text-2xl sm:text-3xl font-bold text-jobblogg-primary mb-1">
                {imageEntries.length}
              </div>
              <TextMuted className="text-xs sm:text-sm">Totalt bilder</TextMuted>
            </div>

            <div className="bg-jobblogg-accent-soft rounded-lg p-3 sm:p-4 text-center">
              <div className="text-base sm:text-lg font-bold text-jobblogg-accent mb-1">
                {userLogEntries.length > 0
                  ? formatShortDate(userLogEntries[0].createdAt)
                  : 'Ingen aktivitet'
                }
              </div>
              <TextMuted className="text-xs sm:text-sm">Siste aktivitet</TextMuted>
            </div>
        </div>

        {/* Share Modal */}
        {showShareModal && (
          <ShareProjectModal
            isOpen={showShareModal}
            onClose={() => setShowShareModal(false)}
            project={project}
            userId={user?.id || ''}
          />
        )}
      </div>
    </div>
    </PageLayout>
  );
};

export default ProjectDetail;

