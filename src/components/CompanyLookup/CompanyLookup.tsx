import React, { useState, useEffect } from 'react';
import { useDebouncedCompanyLookup } from '../../hooks/useCompanyLookup';
import { CompanyInfo } from '../../services/companyLookup';
import { TextInput, PrimaryButton, SecondaryButton } from '../ui';

export interface CompanyLookupProps {
  /** Current company name value */
  companyName: string;
  /** Callback when company name changes */
  onCompanyNameChange: (name: string) => void;
  /** Callback when company is selected and should auto-fill form */
  onCompanySelect: (company: CompanyInfo) => void;
  /** Whether the lookup is disabled */
  disabled?: boolean;
  /** Error message for the company name field */
  error?: string;
  /** Additional CSS classes */
  className?: string;
}

/**
 * Company lookup component with search-as-you-type functionality
 * Integrates with Brønnøysundregisteret for Norwegian company data
 * 
 * @example
 * ```tsx
 * <CompanyLookup
 *   companyName={formData.customerName}
 *   onCompanyNameChange={(name) => updateFormData({ customerName: name })}
 *   onCompanySelect={(company) => {
 *     updateFormData({
 *       customerName: company.name,
 *       orgNumber: company.organizationNumber,
 *       streetAddress: company.visitingAddress?.street,
 *       postalCode: company.visitingAddress?.postalCode,
 *       city: company.visitingAddress?.city
 *     });
 *   }}
 *   error={errors.customerName}
 * />
 * ```
 */
export const CompanyLookup: React.FC<CompanyLookupProps> = ({
  companyName,
  onCompanyNameChange,
  onCompanySelect,
  disabled = false,
  error,
  className = ''
}) => {
  const [showResults, setShowResults] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  
  const {
    results,
    isLoading,
    error: lookupError,
    hasSearched,
    selectedCompany,
    debouncedSearchByName,
    selectCompany,
    clearResults,
    clearError
  } = useDebouncedCompanyLookup(500);

  // Sync search query with company name when manually typed
  useEffect(() => {
    if (companyName !== searchQuery) {
      setSearchQuery(companyName);
      if (companyName.length >= 2) {
        debouncedSearchByName(companyName);
        setShowResults(true);
      } else {
        clearResults();
        setShowResults(false);
      }
    }
  }, [companyName, searchQuery, debouncedSearchByName, clearResults]);

  // Handle company name input change
  const handleCompanyNameChange = (value: string) => {
    setSearchQuery(value);
    onCompanyNameChange(value);
    
    if (value.length >= 2) {
      setShowResults(true);
      clearError();
    } else {
      setShowResults(false);
      clearResults();
    }
  };

  // Handle company selection from results
  const handleCompanySelect = (company: CompanyInfo) => {
    selectCompany(company);
    onCompanySelect(company);
    setShowResults(false);
    clearError();
  };

  // Handle manual search button click
  const handleManualSearch = () => {
    if (companyName.trim().length >= 2) {
      debouncedSearchByName(companyName.trim());
      setShowResults(true);
      clearError();
    }
  };

  // Close results when clicking outside
  const handleBlur = () => {
    // Delay hiding results to allow for clicks on result items
    setTimeout(() => {
      setShowResults(false);
    }, 200);
  };

  return (
    <div className={`relative ${className}`}>
      {/* Company Name Input with Search */}
      <div className="space-y-2">
        <div className="flex gap-2">
          <div className="flex-1">
            <TextInput
              label="Firmanavn"
              placeholder="F.eks. Equinor ASA"
              required
              fullWidth
              value={companyName}
              onChange={(e) => handleCompanyNameChange(e.target.value)}
              onBlur={handleBlur}
              onFocus={() => {
                if (results.length > 0 && hasSearched) {
                  setShowResults(true);
                }
              }}
              error={error}
              disabled={disabled}
              helperText="Start å skrive for å søke i Brønnøysundregisteret"
            />
          </div>
          
          <div className="flex items-end">
            <SecondaryButton
              onClick={handleManualSearch}
              disabled={disabled || isLoading || companyName.trim().length < 2}
              className="min-h-[44px] px-4"
              title="Søk etter firma"
            >
              {isLoading ? (
                <div className="w-5 h-5 border-2 border-jobblogg-primary border-t-transparent rounded-full animate-spin" />
              ) : (
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              )}
            </SecondaryButton>
          </div>
        </div>

        {/* Loading Indicator */}
        {isLoading && (
          <div className="flex items-center gap-2 text-sm text-jobblogg-text-muted">
            <div className="w-4 h-4 border-2 border-jobblogg-primary border-t-transparent rounded-full animate-spin" />
            <span>Søker etter firma...</span>
          </div>
        )}

        {/* Error Message */}
        {lookupError && (
          <div className="bg-jobblogg-error-soft border border-jobblogg-error rounded-lg p-3">
            <div className="flex items-start gap-2">
              <svg className="w-5 h-5 text-jobblogg-error flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <div>
                <p className="text-sm font-medium text-jobblogg-error">
                  {lookupError.message}
                </p>
                {lookupError.details && (
                  <p className="text-xs text-jobblogg-error mt-1 opacity-75">
                    {lookupError.details}
                  </p>
                )}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Search Results Dropdown */}
      {showResults && hasSearched && !isLoading && (
        <div
          className="absolute top-full left-0 right-0 z-50 mt-1 bg-white border border-jobblogg-border rounded-lg shadow-lg max-h-64 overflow-y-auto"
          role="listbox"
          aria-label="Søkeresultater for firma"
        >
          {results.length > 0 ? (
            <div className="py-2">
              <div className="px-3 py-2 text-xs font-medium text-jobblogg-text-muted border-b border-jobblogg-border">
                Fant {results.length} firma{results.length !== 1 ? 'er' : ''}
              </div>
              {results.map((company, index) => (
                <button
                  key={`${company.organizationNumber}-${index}`}
                  onClick={() => handleCompanySelect(company)}
                  className="w-full text-left px-3 py-3 hover:bg-jobblogg-neutral transition-colors duration-150 border-b border-jobblogg-border last:border-b-0 focus:outline-none focus:ring-2 focus:ring-jobblogg-primary focus:ring-inset"
                  role="option"
                  aria-selected="false"
                  tabIndex={0}
                >
                  <div className="space-y-1">
                    <div className="font-medium text-jobblogg-text-strong">
                      {company.name}
                    </div>
                    <div className="text-sm text-jobblogg-text-muted">
                      Org.nr: {company.organizationNumber}
                    </div>
                    {(() => {
                      // Display visiting address if available, otherwise business address
                      const address = company.visitingAddress || company.businessAddress;
                      if (address && (address.street || address.postalCode || address.city)) {
                        return (
                          <div className="text-sm text-jobblogg-text-muted">
                            {address.street && (
                              <>
                                {address.street}
                                {address.postalCode && address.city && (
                                  <>, {address.postalCode} {address.city}</>
                                )}
                              </>
                            )}
                            {!address.street && address.postalCode && address.city && (
                              <>{address.postalCode} {address.city}</>
                            )}
                          </div>
                        );
                      }
                      return null;
                    })()}
                    {company.status !== 'active' && (
                      <div className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-jobblogg-warning-soft text-jobblogg-warning">
                        {company.status === 'dissolved' ? 'Oppløst' : 'Inaktiv'}
                      </div>
                    )}
                  </div>
                </button>
              ))}
            </div>
          ) : (
            <div className="px-3 py-4 text-center text-sm text-jobblogg-text-muted">
              Ingen firma funnet for "{searchQuery}"
              <div className="mt-2 text-xs">
                Prøv et annet søkeord eller skriv inn informasjonen manuelt
              </div>
            </div>
          )}
        </div>
      )}

      {/* Selected Company Confirmation */}
      {selectedCompany && !showResults && (
        <div className="mt-3 bg-jobblogg-success-soft border border-jobblogg-success rounded-lg p-3">
          <div className="flex items-start gap-2">
            <svg className="w-5 h-5 text-jobblogg-success flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <div className="flex-1">
              <p className="text-sm font-medium text-jobblogg-success">
                Firmainformasjon hentet fra Brønnøysundregisteret
              </p>
              <p className="text-xs text-jobblogg-success mt-1 opacity-75">
                Adresse og organisasjonsnummer er automatisk utfylt
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
