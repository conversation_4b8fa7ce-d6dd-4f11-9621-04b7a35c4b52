import React from 'react';

interface LockedInputProps {
  label: string;
  value: string;
  helperText?: string;
  fullWidth?: boolean;
  size?: 'small' | 'medium' | 'large';
  className?: string;
  showLockIcon?: boolean;
  dataSource?: string; // e.g., "Brønnøysundregisteret"
  fetchedAt?: number; // Timestamp when data was fetched
}

/**
 * LockedInput component for displaying read-only form fields with Brønnøysundregisteret data
 * 
 * Features:
 * - WCAG AA compliant read-only styling with proper contrast ratios
 * - Lock icon and data source indicators
 * - Data freshness timestamp display
 * - Consistent styling with JobbLogg design system
 * - Screen reader friendly with proper ARIA attributes
 * 
 * @example
 * ```tsx
 * <LockedInput
 *   label="Organisasjonsnummer"
 *   value="123456789"
 *   helperText="Hentet fra Brønnøysundregisteret"
 *   dataSource="Brønnøysundregisteret"
 *   fetchedAt={Date.now()}
 *   showLockIcon={true}
 * />
 * ```
 */
export const LockedInput: React.FC<LockedInputProps> = ({
  label,
  value,
  helperText,
  fullWidth = false,
  size = 'medium',
  className = '',
  showLockIcon = true,
  dataSource,
  fetchedAt
}) => {
  // Format timestamp for display
  const formatTimestamp = (timestamp: number) => {
    const date = new Date(timestamp);
    return date.toLocaleString('no-NO', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Size classes
  const sizeClasses = {
    small: 'px-3 py-2 text-sm',
    medium: 'px-4 py-3 text-base',
    large: 'px-4 py-4 text-lg'
  };

  return (
    <div className={`${fullWidth ? 'w-full' : ''} ${className}`}>
      {/* Label */}
      <label className="block text-sm font-medium text-jobblogg-text-strong mb-2">
        {label}
        {showLockIcon && (
          <span className="ml-2 inline-flex items-center">
            <svg 
              className="w-4 h-4 text-jobblogg-text-muted" 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
              aria-hidden="true"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={2} 
                d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" 
              />
            </svg>
          </span>
        )}
      </label>

      {/* Input Field */}
      <div className="relative">
        <input
          type="text"
          value={value}
          readOnly
          aria-readonly="true"
          aria-describedby={helperText ? `${label}-helper` : undefined}
          className={`
            ${sizeClasses[size]}
            ${fullWidth ? 'w-full' : ''}
            bg-jobblogg-card-bg
            border border-jobblogg-border
            rounded-lg
            text-jobblogg-text-medium
            cursor-not-allowed
            opacity-75
            focus:outline-none
            focus:ring-2
            focus:ring-jobblogg-primary/30
            focus:border-jobblogg-primary
            transition-colors
            duration-200
          `}
        />
        
        {/* Auto badge */}
        {dataSource && (
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
            <span className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-jobblogg-primary/10 text-jobblogg-primary border border-jobblogg-primary/20">
              Auto
            </span>
          </div>
        )}
      </div>

      {/* Helper Text and Data Source */}
      {(helperText || dataSource || fetchedAt) && (
        <div className="mt-2 space-y-1">
          {helperText && (
            <p id={`${label}-helper`} className="text-sm text-jobblogg-text-muted">
              {helperText}
            </p>
          )}
          
          {dataSource && (
            <div className="flex items-center gap-2 text-xs text-jobblogg-text-muted">
              <svg 
                className="w-3 h-3" 
                fill="none" 
                stroke="currentColor" 
                viewBox="0 0 24 24"
                aria-hidden="true"
              >
                <path 
                  strokeLinecap="round" 
                  strokeLinejoin="round" 
                  strokeWidth={2} 
                  d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" 
                />
              </svg>
              <span>Hentet fra {dataSource}</span>
              {fetchedAt && (
                <span className="text-jobblogg-text-muted/75">
                  • Oppdatert: {formatTimestamp(fetchedAt)}
                </span>
              )}
            </div>
          )}
        </div>
      )}
    </div>
  );
};
