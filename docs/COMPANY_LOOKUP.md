# Company Lookup Feature

## Overview

The Company Lookup feature integrates with Brønnøysundregisteret (Norwegian Business Register) to provide automatic company information lookup and form auto-fill functionality in JobbLogg.

## Features

### 🔍 **Search-as-you-type**
- Real-time company search while typing
- Debounced API calls to prevent excessive requests
- Displays up to 10 matching companies

### 📋 **Auto-fill Form Fields**
- **Company Name**: Automatically filled with official registered name
- **Organization Number**: Auto-populated with 9-digit org number
- **Address**: Uses visiting address (besøksadresse) if available, falls back to business address
- **Structured Address**: Fills streetAddress, postalCode, and city fields

### 🎯 **Smart Integration**
- Only appears for "Firma" customer type
- Maintains manual input as fallback option
- Works in both CreateProject form and Step2CustomerInfo wizard

## Usage

### In Project Creation Forms

When a user selects "Firma" as customer type, the regular name input field is replaced with the CompanyLookup component:

```tsx
{formData.customerType === 'firma' ? (
  <CompanyLookup
    companyName={formData.customerName}
    onCompanyNameChange={(name) => updateFormData({ customerName: name })}
    onCompanySelect={(company) => {
      updateFormData({
        customerName: company.name,
        orgNumber: company.organizationNumber,
        streetAddress: company.visitingAddress?.street,
        postalCode: company.visitingAddress?.postalCode,
        city: company.visitingAddress?.city
      });
    }}
    error={errors.customerName}
  />
) : (
  // Regular text input for private customers
)}
```

### API Integration

The feature uses Brønnøysundregisteret's open data API:
- **Base URL**: `https://data.brreg.no/enhetsregisteret/api/enheter`
- **Search**: `/enheter?navn={query}&size={limit}`
- **Lookup**: `/enheter/{orgNumber}`

## Components

### CompanyLookup Component
- **Location**: `src/components/CompanyLookup/CompanyLookup.tsx`
- **Props**: `companyName`, `onCompanyNameChange`, `onCompanySelect`, `error`, `disabled`
- **Features**: Search dropdown, loading states, error handling, accessibility

### useCompanyLookup Hook
- **Location**: `src/hooks/useCompanyLookup.ts`
- **Features**: State management, debounced search, race condition handling
- **Variants**: `useCompanyLookup()`, `useDebouncedCompanyLookup(delay)`

### Company Lookup Service
- **Location**: `src/services/companyLookup.ts`
- **Functions**: `searchCompanies()`, `getCompanyByOrgNumber()`
- **Features**: Error handling, data transformation, validation

## Error Handling

The system gracefully handles various error scenarios:

- **Network Errors**: Shows user-friendly message about connection issues
- **API Errors**: Displays specific error messages from the API
- **Rate Limiting**: Informs user to wait before trying again
- **No Results**: Shows helpful message suggesting manual input
- **Invalid Input**: Validates organization numbers and search queries

## Accessibility

- **WCAG AA Compliant**: Proper contrast ratios and focus management
- **Keyboard Navigation**: Full keyboard support for search and selection
- **Screen Reader Support**: ARIA labels and roles for assistive technology
- **Touch Targets**: Minimum 44px touch targets for mobile devices

## Testing

### Manual Testing
1. Navigate to project creation form
2. Select "Firma" as customer type
3. Start typing a Norwegian company name (e.g., "Equinor")
4. Select a company from the dropdown
5. Verify that form fields are auto-filled

### Test Page
Access the dedicated test page at `/company-lookup-test.html` to:
- Test search functionality
- Test organization number lookup
- View API responses and errors
- Test with known Norwegian companies

### Browser Console Testing
```javascript
// Test company search
testCompanySearch('Equinor');

// Test organization number lookup
testOrgNumberLookup('*********');

// Run full test suite
runTestSuite();
```

## Known Companies for Testing

| Company | Organization Number | Use Case | Address Type |
|---------|-------------------|----------|--------------|
| Equinor ASA | ********* | Large oil company | Business address only |
| DNB Bank ASA | ********* | Major bank | Business address only |
| Telenor ASA | ********* | Telecommunications | Business address only |
| Norsk Hydro ASA | ********* | Industrial company | Mixed |

**Note**: Many large Norwegian companies only have `forretningsadresse` (business address) registered, not `beliggenhetsadresse` (visiting address). The system automatically uses business address as fallback.

## Configuration

### Environment Variables
No additional environment variables required. The feature uses the public Brønnøysundregisteret API.

### Rate Limiting
The API has built-in rate limiting. The service handles 429 responses gracefully and informs users to wait.

## Future Enhancements

- **Caching**: Implement local caching for frequently searched companies
- **Favorites**: Allow users to save frequently used companies
- **Advanced Search**: Add filters for industry, location, company size
- **Bulk Import**: Support for importing multiple companies from CSV
- **Company Validation**: Real-time validation of organization numbers

## Troubleshooting

### Common Issues

1. **No search results**: Verify internet connection and try different search terms
2. **API errors**: Check browser console for detailed error messages
3. **"Ingen adresse registrert" showing**: This was fixed - the system now uses business address (`forretningsadresse`) as fallback when visiting address (`beliggenhetsadresse`) is not available
4. **Auto-fill not working**: Ensure the company has registered address information in either visiting or business address fields
5. **Slow responses**: API response times may vary; loading indicators show progress

### Debug Tools

- Use the test page at `/company-lookup-test.html`
- Check browser console for detailed API responses
- Use browser network tab to inspect API calls
- Test with known organization numbers for verification
