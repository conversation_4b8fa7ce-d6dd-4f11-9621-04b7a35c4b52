import { mutation, query } from './_generated/server';
import { v } from 'convex/values';

// Create a new customer
export const create = mutation({
  args: {
    name: v.string(),
    type: v.union(v.literal("privat"), v.literal("firma"), v.literal("bedrift")),
    contactPerson: v.optional(v.string()),
    phone: v.optional(v.string()),
    email: v.optional(v.string()),
    // Enhanced address fields
    address: v.optional(v.string()),        // Legacy field for backward compatibility
    streetAddress: v.optional(v.string()), // New structured address fields
    postalCode: v.optional(v.string()),
    city: v.optional(v.string()),
    entrance: v.optional(v.string()),
    orgNumber: v.optional(v.string()),
    notes: v.optional(v.string()),
    userId: v.string()
  },
  handler: async (ctx, args) => {
    // Validate required fields based on customer type
    if ((args.type === "firma" || args.type === "bedrift") && args.orgNumber && args.orgNumber.trim() === "") {
      throw new Error("Organisasjonsnummer kan ikke være tomt for bedrift");
    }

    // Validate address fields - either legacy address OR new structured fields
    const hasLegacyAddress = args.address && args.address.trim();
    const hasStructuredAddress = args.streetAddress && args.postalCode && args.city;

    if (!hasLegacyAddress && !hasStructuredAddress) {
      throw new Error("Adresse er påkrevd");
    }

    return await ctx.db.insert("customers", {
      name: args.name.trim(),
      type: args.type,
      contactPerson: args.contactPerson?.trim() || undefined,
      phone: args.phone?.trim() || undefined,
      email: args.email?.trim() || undefined,
      // Address fields
      address: args.address?.trim() || undefined,
      streetAddress: args.streetAddress?.trim() || undefined,
      postalCode: args.postalCode?.trim() || undefined,
      city: args.city?.trim() || undefined,
      entrance: args.entrance?.trim() || undefined,
      orgNumber: args.orgNumber?.trim() || undefined,
      notes: args.notes?.trim() || undefined,
      userId: args.userId,
      createdAt: Date.now()
    });
  }
});

// Get all customers for a user
export const getByUser = query({
  args: { userId: v.string() },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("customers")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .order("desc")
      .collect();
  }
});

// Get customer by ID
export const getById = query({
  args: { customerId: v.id("customers") },
  handler: async (ctx, args) => {
    const customer = await ctx.db.get(args.customerId);
    return customer || null;
  }
});

// Get customers by type for a user
export const getByUserAndType = query({
  args: { 
    userId: v.string(),
    type: v.union(v.literal("privat"), v.literal("firma"))
  },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("customers")
      .withIndex("by_user_and_type", (q) => q.eq("userId", args.userId).eq("type", args.type))
      .order("desc")
      .collect();
  }
});

// Search customers by name or organization number (AI-agent friendly)
export const search = query({
  args: { 
    userId: v.string(),
    searchTerm: v.string()
  },
  handler: async (ctx, args) => {
    const customers = await ctx.db
      .query("customers")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .collect();

    const searchLower = args.searchTerm.toLowerCase();
    
    return customers.filter(customer => 
      customer.name.toLowerCase().includes(searchLower) ||
      (customer.orgNumber && customer.orgNumber.includes(args.searchTerm)) ||
      (customer.contactPerson && customer.contactPerson.toLowerCase().includes(searchLower)) ||
      (customer.address && customer.address.toLowerCase().includes(searchLower))
    );
  }
});

// Update customer
export const update = mutation({
  args: {
    customerId: v.id("customers"),
    userId: v.string(),
    name: v.optional(v.string()),
    type: v.optional(v.union(v.literal("privat"), v.literal("firma"))),
    contactPerson: v.optional(v.string()),
    phone: v.optional(v.string()),
    email: v.optional(v.string()),
    // Enhanced address fields
    address: v.optional(v.string()),
    streetAddress: v.optional(v.string()),
    postalCode: v.optional(v.string()),
    city: v.optional(v.string()),
    entrance: v.optional(v.string()),
    orgNumber: v.optional(v.string()),
    notes: v.optional(v.string())
  },
  handler: async (ctx, args) => {
    // Verify customer exists and belongs to user
    const customer = await ctx.db.get(args.customerId);
    if (!customer) {
      throw new Error("Kunde ikke funnet");
    }
    if (customer.userId !== args.userId) {
      throw new Error("Du har ikke tilgang til å redigere denne kunden");
    }

    // Build update object with only provided fields
    const updates: any = {};
    if (args.name !== undefined) updates.name = args.name.trim();
    if (args.type !== undefined) updates.type = args.type;
    if (args.contactPerson !== undefined) updates.contactPerson = args.contactPerson?.trim() || undefined;
    if (args.phone !== undefined) updates.phone = args.phone?.trim() || undefined;
    if (args.email !== undefined) updates.email = args.email?.trim() || undefined;
    // Address fields
    if (args.address !== undefined) updates.address = args.address?.trim() || undefined;
    if (args.streetAddress !== undefined) updates.streetAddress = args.streetAddress?.trim() || undefined;
    if (args.postalCode !== undefined) updates.postalCode = args.postalCode?.trim() || undefined;
    if (args.city !== undefined) updates.city = args.city?.trim() || undefined;
    if (args.entrance !== undefined) updates.entrance = args.entrance?.trim() || undefined;
    if (args.orgNumber !== undefined) updates.orgNumber = args.orgNumber?.trim() || undefined;
    if (args.notes !== undefined) updates.notes = args.notes?.trim() || undefined;

    await ctx.db.patch(args.customerId, updates);
    return { success: true };
  }
});

// Delete customer (only if no projects are associated)
export const deleteCustomer = mutation({
  args: {
    customerId: v.id("customers"),
    userId: v.string()
  },
  handler: async (ctx, args) => {
    // Verify customer exists and belongs to user
    const customer = await ctx.db.get(args.customerId);
    if (!customer) {
      throw new Error("Kunde ikke funnet");
    }
    if (customer.userId !== args.userId) {
      throw new Error("Du har ikke tilgang til å slette denne kunden");
    }

    // Check if customer has associated projects
    const projects = await ctx.db
      .query("projects")
      .withIndex("by_customer", (q) => q.eq("customerId", args.customerId))
      .collect();

    if (projects.length > 0) {
      throw new Error("Kan ikke slette kunde som har tilknyttede prosjekter. Slett prosjektene først.");
    }

    await ctx.db.delete(args.customerId);
    return { success: true };
  }
});

// Get customer statistics (AI-agent friendly for reporting)
export const getStats = query({
  args: { userId: v.string() },
  handler: async (ctx, args) => {
    const customers = await ctx.db
      .query("customers")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .collect();

    const privatCustomers = customers.filter(c => c.type === "privat").length;
    const firmaCustomers = customers.filter(c => c.type === "firma").length;

    return {
      total: customers.length,
      privat: privatCustomers,
      firma: firmaCustomers,
      recentCustomers: customers
        .sort((a, b) => b.createdAt - a.createdAt)
        .slice(0, 5)
    };
  }
});
