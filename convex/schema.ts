import { defineSchema, defineTable } from 'convex/server';
import { v } from 'convex/values';

export default defineSchema({
  // Customer data table for AI-agent friendly structure
  customers: defineTable({
    name: v.string(),                    // Customer name (required) - shown in UI and search
    type: v.union(v.literal("privat"), v.literal("firma"), v.literal("bedrift")), // Customer type: "privat", "firma" (legacy), or "bedrift" - controls logic and display
    contactPerson: v.optional(v.string()), // Contact person (if type === "firma" or "bedrift") - e.g. "<PERSON>rne Løken"
    phone: v.optional(v.string()),       // Phone number (optional) - for quick contact
    email: v.optional(v.string()),       // Email address (optional) - used in reporting or notifications
    // Enhanced address structure
    address: v.optional(v.string()),     // Legacy single address field (for backward compatibility)
    streetAddress: v.optional(v.string()), // Street address (required for new customers) - e.g. "Storgata 15"
    postalCode: v.optional(v.string()),  // Postal code (required for new customers) - e.g. "0123"
    city: v.optional(v.string()),        // City/Town (required for new customers) - e.g. "Oslo"
    entrance: v.optional(v.string()),    // Entrance/Floor info (optional) - e.g. "Oppgang A, 2. etasje"
    orgNumber: v.optional(v.string()),   // Organization number (optional, only for firma/bedrift) - legal ID
    notes: v.optional(v.string()),       // Notes (optional) - free text: key code, "customer is allergic to dogs", etc.
    // Brønnøysundregisteret data tracking
    brregFetchedAt: v.optional(v.number()), // Timestamp when data was fetched from Brønnøysundregisteret
    brregData: v.optional(v.object({     // Original Brønnøysundregisteret data (for reference and freshness)
      name: v.optional(v.string()),      // Company name from Brreg
      orgNumber: v.optional(v.string()), // Organization number from Brreg
      managingDirector: v.optional(v.string()), // Managing director from Brreg
      businessAddress: v.optional(v.object({ // Business address from Brreg
        street: v.optional(v.string()),
        postalCode: v.optional(v.string()),
        city: v.optional(v.string()),
        municipality: v.optional(v.string())
      })),
      visitingAddress: v.optional(v.object({ // Visiting address from Brreg (if different)
        street: v.optional(v.string()),
        postalCode: v.optional(v.string()),
        city: v.optional(v.string()),
        municipality: v.optional(v.string())
      }))
    })),
    // Address override tracking
    useCustomAddress: v.optional(v.boolean()), // Whether user chose to override Brreg address
    userId: v.string(),                  // Owner of this customer record
    createdAt: v.number()                // Creation timestamp
  })
    .index("by_user", ["userId"])
    .index("by_type", ["type"])
    .index("by_user_and_type", ["userId", "type"])
    .index("by_org_number", ["orgNumber"]),

  projects: defineTable({
    name: v.string(),
    description: v.string(),
    userId: v.string(),
    customerId: v.optional(v.id("customers")), // Reference to customer - enables AI queries like "projects for customer X"
    sharedId: v.string(),
    createdAt: v.number(),
    // Archive management - preserves data while removing from active view
    isArchived: v.optional(v.boolean()),       // Archive status - defaults to false for active projects
    archivedAt: v.optional(v.number()),        // Timestamp when project was archived
    archivedBy: v.optional(v.string()),        // User ID who archived the project
    // Project sharing configuration
    isPubliclyShared: v.optional(v.boolean()), // Enable/disable public sharing
    shareSettings: v.optional(v.object({
      showContractorNotes: v.boolean(),        // Show contractor-specific notes to customers
      accessCount: v.number(),                 // Track how many times project was accessed
      lastAccessedAt: v.optional(v.number())   // Track last access time
    })),
    // Job information for contractor workflow documentation
    jobData: v.optional(v.object({
      jobDescription: v.string(),           // "Hva skal gjøres?" - detailed job description
      photos: v.array(v.object({           // "Bilder fra befaring" - site inspection photos
        url: v.string(),                   // Image URL from Convex storage
        note: v.optional(v.string()),      // Optional comment/note for the image
        capturedAt: v.optional(v.number()) // Timestamp when photo was taken
      })),
      accessNotes: v.string(),             // "Tilkomst og forhold" - access and site conditions
      equipmentNeeds: v.string(),          // "Hva må medbringes?" - equipment and materials needed
      unresolvedQuestions: v.string(),     // "Hva må avklares?" - questions that need clarification
      personalNotes: v.string()            // "Egne notater" - contractor's personal notes
    }))
  })
    .index("by_user", ["userId"])
    .index("by_shared_id", ["sharedId"])
    .index("by_customer", ["customerId"])
    .index("by_user_and_customer", ["userId", "customerId"])
    .index("by_user_and_archive_status", ["userId", "isArchived"])  // Efficient querying of active vs archived projects
    .index("by_archived_status", ["isArchived"]),                   // Global archive status queries

  logEntries: defineTable({
    projectId: v.id("projects"),
    userId: v.string(),
    description: v.string(),
    imageId: v.optional(v.id("_storage")),
    createdAt: v.number(),
    // Entry type to distinguish between user content and system activities
    entryType: v.optional(v.union(v.literal("user"), v.literal("system"))), // "user" for regular entries, "system" for archive/restore activities
    // Edit history and tracking fields
    isEdited: v.optional(v.boolean()),
    lastEditedAt: v.optional(v.number()),
    editHistory: v.optional(v.array(v.object({
      version: v.number(),
      editedAt: v.number(),
      description: v.string(),
      imageId: v.optional(v.id("_storage")),
      changeType: v.string(), // "description", "image", "both"
      changeSummary: v.string()
    })))
  })
    .index("by_project", ["projectId"])
    .index("by_user", ["userId"])
    .index("by_project_and_user", ["projectId", "userId"]),



  // Customer image likes - customers can "like" images in shared projects
  imageLikes: defineTable({
    logEntryId: v.id("logEntries"),           // Reference to the log entry with the image
    projectId: v.id("projects"),              // Reference to project (for easier querying)
    sharedId: v.string(),                     // For validation that like came from shared link

    // Customer identification (anonymous but consistent within session)
    customerSessionId: v.string(),            // Unique session identifier (nanoid) for anonymous customer
    customerName: v.optional(v.string()),     // Optional name if customer provided it
    customerEmail: v.optional(v.string()),    // Optional email if customer provided it

    // Like metadata
    createdAt: v.number(),
    ipAddress: v.optional(v.string()),        // For spam prevention
  })
    .index("by_log_entry", ["logEntryId"])
    .index("by_project", ["projectId"])
    .index("by_shared_id", ["sharedId"])
    .index("by_customer_session", ["customerSessionId"])
    .index("by_log_entry_and_customer", ["logEntryId", "customerSessionId"])
    .index("by_project_and_customer", ["projectId", "customerSessionId"]),

  // Chat messages - threaded conversations on log entries
  messages: defineTable({
    logId: v.id("logEntries"),                // Always linked to a log entry (Prosjekt → Logg → Tråd)
    parentId: v.optional(v.id("messages")),   // null = root message (log description), value = reply to message

    // Sender information
    senderId: v.string(),                     // User ID of message sender
    senderRole: v.union(v.literal("customer"), v.literal("contractor")), // Role for display name mapping

    // Message content
    text: v.optional(v.string()),             // Message text content (optional if file-only message)
    file: v.optional(v.object({               // File attachment metadata
      url: v.string(),                        // Signed URL to file storage
      name: v.string(),                       // Original filename
      size: v.number(),                       // File size in bytes
      type: v.string(),                       // MIME type
      thumbnailUrl: v.optional(v.string())    // Thumbnail URL for images/videos
    })),

    // Reactions and engagement
    reactions: v.optional(v.array(v.object({
      emoji: v.string(),                      // Emoji character (e.g., "👍", "❤️")
      userIds: v.array(v.string()),           // Array of user IDs who reacted with this emoji
      count: v.number()                       // Count of reactions (for performance)
    }))),

    // Read status tracking
    readBy: v.optional(v.record(v.string(), v.number())), // Dynamic object: { [userId]: timestamp }

    // Delivery status tracking
    deliveryStatus: v.optional(v.union(
      v.literal("sending"),     // Message is being sent
      v.literal("sent"),        // Message sent to server
      v.literal("delivered"),   // Message delivered to recipient(s)
      v.literal("failed")       // Message failed to send
    )),
    deliveredTo: v.optional(v.record(v.string(), v.number())), // { [userId]: timestamp } when delivered
    failureReason: v.optional(v.string()), // Error message if delivery failed

    // Message metadata
    createdAt: v.number(),                    // Creation timestamp
    updatedAt: v.optional(v.number()),        // Last edit timestamp
    isEdited: v.optional(v.boolean()),        // Whether message has been edited
    isDeleted: v.optional(v.boolean())        // Soft delete flag
  })
    .index("by_log", ["logId"])               // Get all messages for a log entry
    .index("by_parent", ["parentId"])         // Get replies to a message
    .index("by_sender", ["senderId"])         // Get messages by sender
    .index("by_created", ["createdAt"])       // Chronological ordering
    .index("by_log_and_created", ["logId", "createdAt"]) // Efficient log message ordering
    .index("by_log_and_parent", ["logId", "parentId"]),   // Thread structure queries

  // Typing indicators for real-time chat (updated for proper deployment)
  typingIndicators: defineTable({
    logId: v.id("logEntries"),                // Log entry where user is typing
    userId: v.string(),                       // User who is typing
    userRole: v.union(v.literal("customer"), v.literal("contractor")), // Role for display
    expiresAt: v.number(),                    // Timestamp when indicator expires
    createdAt: v.number(),                    // When typing started
    updatedAt: v.number()                     // Last activity timestamp
  })
    .index("by_log", ["logId"])               // Get all typing indicators for a log
    .index("by_log_and_user", ["logId", "userId"]) // Get specific user's typing indicator
    .index("by_expires", ["expiresAt"]),      // For cleanup of expired indicators

  // Link previews for OpenGraph metadata caching
  linkPreviews: defineTable({
    url: v.string(),                          // Original URL
    title: v.optional(v.string()),            // OpenGraph title
    description: v.optional(v.string()),      // OpenGraph description
    image: v.optional(v.string()),            // OpenGraph image URL
    siteName: v.optional(v.string()),         // OpenGraph site name
    type: v.optional(v.string()),             // OpenGraph type (article, website, etc.)
    domain: v.string(),                       // Extracted domain for display
    favicon: v.optional(v.string()),          // Site favicon URL
    cachedAt: v.number(),                     // When data was cached
    expiresAt: v.number(),                    // When cache expires
    fetchError: v.optional(v.string())        // Error message if fetch failed
  })
    .index("by_url", ["url"])                 // Get cached data by URL
    .index("by_expires", ["expiresAt"])       // For cleanup of expired cache entries
    .index("by_domain", ["domain"])           // Group by domain for analytics
});
